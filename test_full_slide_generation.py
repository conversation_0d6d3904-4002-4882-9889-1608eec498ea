"""
Test full slide generation process
"""

import requests
import json

def test_full_slide_generation():
    """Test toàn bộ quy trình tạo slide"""
    
    # Template ID đã test thành công
    template_id = "1hiKvx_qogkBh7jxfowHso9WYvA77NvFUpz3cRICzdxg"
    
    # Lesson ID mẫu - cần thay thế bằng lesson thực tế
    lesson_id = "lesson_test_123"
    
    print("🚀 TESTING FULL SLIDE GENERATION")
    print("=" * 50)
    print(f"Template ID: {template_id}")
    print(f"Lesson ID: {lesson_id}")
    print()
    
    # Test sync slide generation
    payload = {
        "lesson_id": lesson_id,
        "template_id": template_id,
        "config_prompt": "Tạo slide với phong cách sinh động, phù hợp với học sinh trung học phổ thông. Sử dụng ngôn ngữ đơn giản, d<PERSON> hiểu.",
        "presentation_title": "Bài học H<PERSON>a học - Test API"
    }
    
    print("📤 SENDING REQUEST...")
    print(f"Payload: {json.dumps(payload, indent=2, ensure_ascii=False)}")
    print()
    
    try:
        url = "http://localhost:8000/api/v1/slides/generate-slides"
        response = requests.post(url, json=payload)
        
        print(f"📥 RESPONSE:")
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ SLIDE GENERATION SUCCESS!")
            print(f"  Presentation ID: {result.get('presentation_id')}")
            print(f"  Presentation Title: {result.get('presentation_title')}")
            print(f"  Web View Link: {result.get('web_view_link')}")
            print(f"  Slides Created: {result.get('slides_created')}")
            print(f"  Template Used: {result.get('template_info', {}).get('title')}")
            
            # Mở link trong browser nếu có
            web_link = result.get('web_view_link')
            if web_link:
                print(f"\n🔗 OPEN LINK: {web_link}")
                
        else:
            print("❌ SLIDE GENERATION FAILED!")
            try:
                error_detail = response.json()
                print(f"Error: {json.dumps(error_detail, indent=2, ensure_ascii=False)}")
            except:
                print(f"Raw error: {response.text}")
                
    except Exception as e:
        print(f"❌ EXCEPTION: {e}")

def test_with_mock_lesson():
    """Test với lesson content giả lập"""
    print("\n" + "=" * 50)
    print("🧪 TESTING WITH MOCK LESSON CONTENT")
    print("=" * 50)
    
    # Tạo mock lesson content bằng cách gọi trực tiếp service
    print("💡 Suggestion: Tạo mock lesson content trong database hoặc")
    print("   sử dụng lesson_id thực tế từ textbook đã import")
    print()
    
    # Kiểm tra xem có textbook nào không
    try:
        # Thử gọi health check của PDF service để xem có textbook nào
        response = requests.get("http://localhost:8000/api/v1/pdf/health")
        if response.status_code == 200:
            health_info = response.json()
            print("📚 PDF/Textbook Service Status:")
            print(f"  Status: {health_info.get('status')}")
            
            # Gợi ý cách lấy lesson_id thực tế
            print("\n💡 TO GET REAL LESSON_ID:")
            print("1. Import a textbook using /api/v1/pdf/import")
            print("2. Get lesson list using appropriate endpoint")
            print("3. Use real lesson_id in slide generation")
            
    except Exception as e:
        print(f"❌ Could not check textbook service: {e}")

if __name__ == "__main__":
    test_full_slide_generation()
    test_with_mock_lesson()
