import logging
import threading
import os
import json
from typing import Dict, Any, List, Optional
from google.auth.transport.requests import Request
from google.oauth2.credentials import Credentials
from google_auth_oauthlib.flow import InstalledAppFlow
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError

from app.core.config import settings

logger = logging.getLogger(__name__)


def extract_text_from_shape(shape: Dict[str, Any]) -> str:
    if 'text' in shape and 'textElements' in shape['text']:
        parts = []
        for te in shape['text']['textElements']:
            text_run = te.get('textRun')
            if text_run:
                parts.append(text_run.get('content', '').strip())
        return ' '.join(parts).strip()
    return ''


class GoogleSlidesService:
    _instance = None
    _lock = threading.Lock()

    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super(GoogleSlidesService, cls).__new__(cls)
                    cls._instance._initialized = False
        return cls._instance

    def __init__(self):
        if self._initialized:
            return
        self.slides_service = None
        self.drive_service = None
        self.credentials = None
        self._service_initialized = False
        self._initialized = True

    def _ensure_service_initialized(self):
        if not self._service_initialized:
            logger.info("🔄 GoogleSlidesService: First-time initialization triggered")
            self._initialize_service()
            self._service_initialized = True
            logger.info("✅ GoogleSlidesService: Initialization completed")

    def _initialize_service(self):
        """Khởi tạo Google Slides service với OAuth 2.0"""
        try:
            credentials_path = "google_client_Dat.json"
            token_path = "token.json"  # File lưu token sau khi authenticate

            if not os.path.exists(credentials_path):
                logger.warning("""
Google Slides service requires OAuth 2.0 Client credentials.
Please ensure google_client_Dat.json exists in the project root.
Service will be disabled.
                """)
                return

            # Scopes cần thiết
            SCOPES = [
                'https://www.googleapis.com/auth/drive',
                'https://www.googleapis.com/auth/presentations',
                'https://www.googleapis.com/auth/drive.file',
                'https://www.googleapis.com/auth/gmail.modify'
            ]

            creds = None

            # Kiểm tra xem đã có token chưa
            if os.path.exists(token_path):
                creds = Credentials.from_authorized_user_file(token_path, SCOPES)

            # Nếu không có credentials hợp lệ, thực hiện OAuth flow
            if not creds or not creds.valid:
                if creds and creds.expired and creds.refresh_token:
                    # Refresh token nếu expired
                    creds.refresh(Request())
                else:
                    # Thực hiện OAuth flow mới
                    flow = InstalledAppFlow.from_client_secrets_file(
                        credentials_path, SCOPES
                    )
                    # Sử dụng local server để nhận callback
                    creds = flow.run_local_server()

                # Lưu token để sử dụng lần sau
                with open(token_path, 'w') as token:
                    token.write(creds.to_json())

            self.credentials = creds

            # Tạo services
            self.slides_service = build('slides', 'v1', credentials=self.credentials)
            self.drive_service = build('drive', 'v3', credentials=self.credentials)
            logger.info("Google Slides service initialized with OAuth 2.0")

        except Exception as e:
            logger.error(f"Failed to initialize Google Slides service: {e}")
            self.slides_service = None
            self.drive_service = None

    def is_available(self) -> bool:
        self._ensure_service_initialized()
        return self.slides_service is not None and self.drive_service is not None

    async def copy_and_analyze_template(self, template_id: str, new_title: str) -> Dict[str, Any]:
        """
        Copy template và phân tích cấu trúc của bản sao (theo yêu cầu mới)

        Args:
            template_id: ID của Google Slides template gốc
            new_title: Tên cho file mới

        Returns:
            Dict chứa thông tin file đã copy và cấu trúc slides/elements
        """
        if not self.is_available():
            return {
                "success": False,
                "error": "Google Slides service not available"
            }

        try:
            # Bước 1: Copy template thành file mới ngay từ đầu
            logger.info(f"Copying template {template_id} to new file: {new_title}")
            copy_result = await self.copy_template(template_id, new_title)
            if not copy_result["success"]:
                return {
                    "success": False,
                    "error": f"Failed to copy template: {copy_result['error']}"
                }

            copied_presentation_id = copy_result["file_id"]
            logger.info(f"Template copied successfully. New presentation ID: {copied_presentation_id}")

            # Bước 2: Phân tích cấu trúc của bản sao (không phải template gốc)
            presentation = self.slides_service.presentations().get(
                presentationId=copied_presentation_id
            ).execute()

            slides_info = []
            for slide in presentation.get('slides', []):
                slide_info = {
                    "slideId": slide.get("objectId"),
                    "elements": []
                }

                for element in slide.get('pageElements', []):
                    if 'shape' in element:
                        text = extract_text_from_shape(element['shape'])

                        # Lấy size và transform từ element (không phải elementProperties)
                        size = element.get('size', {})
                        transform = element.get('transform', {})

                        slide_info['elements'].append({
                            "objectId": element.get('objectId'),
                            "text": text,
                            "size": size,  # Trả về toàn bộ size object
                            "transform": transform  # Trả về toàn bộ transform object
                        })

                slides_info.append(slide_info)

            return {
                "success": True,
                "original_template_id": template_id,
                "copied_presentation_id": copied_presentation_id,
                "presentation_title": presentation.get('title', 'Untitled'),
                "web_view_link": copy_result["web_view_link"],
                "slide_count": len(presentation.get('slides', [])),
                "slides": slides_info
            }

        except HttpError as e:
            logger.error(f"HTTP error in copy_and_analyze_template: {e}")
            return {
                "success": False,
                "error": f"HTTP error: {e}"
            }
        except Exception as e:
            logger.error(f"Error in copy_and_analyze_template: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    async def analyze_template_structure(self, template_id: str) -> Dict[str, Any]:
        """
        Phân tích cấu trúc template Google Slides (lấy text trên các slide)
        DEPRECATED: Sử dụng copy_and_analyze_template thay thế

        Args:
            template_id: ID của Google Slides template

        Returns:
            Dict chứa thông tin slides, objectId và text đang hiển thị
        """
        if not self.is_available():
            return {
                "success": False,
                "error": "Google Slides service not available"
            }

        try:
            presentation = self.slides_service.presentations().get(
                presentationId=template_id
            ).execute()

            slides_info = []
            for slide in presentation.get('slides', []):
                slide_info = {
                    "slideId": slide.get("objectId"),
                    "elements": []
                }

                for element in slide.get('pageElements', []):
                    if 'shape' in element:
                        text = extract_text_from_shape(element['shape'])

                        # Lấy size và transform từ element (không phải elementProperties)
                        size = element.get('size', {})
                        transform = element.get('transform', {})

                        slide_info['elements'].append({
                            "objectId": element.get('objectId'),
                            "text": text,
                            "size": size,  # Trả về toàn bộ size object
                            "transform": transform  # Trả về toàn bộ transform object
                        })

                slides_info.append(slide_info)


            return {
                "success": True,
                "template_id": template_id,
                "title": presentation.get('title', 'Untitled'),
                "slide_count": len(presentation.get('slides', [])),
                "slides": slides_info
            }

        except HttpError as e:
            logger.error(f"HTTP error analyzing template {template_id}: {e}")
            return {
                "success": False,
                "error": f"HTTP error: {e}"
            }
        except Exception as e:
            logger.error(f"Error analyzing template {template_id}: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    async def copy_template(self, template_id: str, new_title: str) -> Dict[str, Any]:
        if not self.is_available():
            return {
                "success": False,
                "error": "Google Slides service not available"
            }

        try:
            copied_file = self.drive_service.files().copy(
                fileId=template_id,
                body={'name': new_title}
            ).execute()

            self.drive_service.permissions().create(
            fileId=copied_file.get('id'),
            body={
                'type': 'anyone',
                'role': 'writer'
            },
            fields='id',
            supportsAllDrives=True,
            sendNotificationEmail=False
            ).execute()


            return {
                "success": True,
                "file_id": copied_file.get('id'),
                "name": copied_file.get('name'),
                "web_view_link": f"https://docs.google.com/presentation/d/{copied_file.get('id')}/edit"
            }

        except HttpError as e:
            logger.error(f"HTTP error copying template {template_id}: {e}")
            return {
                "success": False,
                "error": f"HTTP error: {e}"
            }
        except Exception as e:
            logger.error(f"Error copying template {template_id}: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    async def update_copied_presentation_content(
        self,
        presentation_id: str,
        slides_content: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """
        Cập nhật nội dung vào presentation đã copy (theo quy trình mới)

        Args:
            presentation_id: ID của presentation đã copy
            slides_content: List nội dung slides từ LLM

        Returns:
            Dict kết quả cập nhật nội dung
        """
        if not self.is_available():
            return {
                "success": False,
                "error": "Google Slides service not available"
            }

        try:
            requests = []

            logger.info(f"Updating presentation {presentation_id} with {len(slides_content)} slides")

            # Lấy thông tin presentation hiện tại
            presentation = self.slides_service.presentations().get(
                presentationId=presentation_id
            ).execute()

            logger.info(f"Current presentation has {len(presentation.get('slides', []))} slides")

            # Xử lý từng slide từ LLM
            slides_created = 0
            slides_updated = 0

            for slide_content in slides_content:
                slide_id = slide_content.get('slideId')
                action = slide_content.get('action', 'update')
                updates = slide_content.get('updates', {})

                if not slide_id or not updates:
                    logger.warning(f"Skipping slide with missing slideId or updates: {slide_content}")
                    continue

                logger.info(f"Processing slide {slide_id} with action '{action}' and {len(updates)} updates")

                # Xử lý tạo slide mới
                if action == 'create':
                    base_slide_id = slide_content.get('baseSlideId')
                    if base_slide_id:
                        # Tạo slide mới bằng cách duplicate slide base
                        requests.append({
                            'duplicateObject': {
                                'objectId': base_slide_id,
                                'objectIds': {
                                    base_slide_id: slide_id
                                }
                            }
                        })
                        slides_created += 1
                        logger.info(f"Creating new slide {slide_id} based on {base_slide_id}")
                    else:
                        # Tạo slide trống mới
                        requests.append({
                            'createSlide': {
                                'objectId': slide_id,
                                'insertionIndex': len(presentation.get('slides', [])) + slides_created
                            }
                        })
                        slides_created += 1
                        logger.info(f"Creating new blank slide {slide_id}")

                # Cập nhật nội dung cho slide (cả slide cũ và mới)
                for element_id, new_content in updates.items():
                    if not new_content:
                        continue

                    # Làm sạch nội dung
                    clean_content = str(new_content).strip()
                    if not clean_content:
                        continue

                    # Xóa nội dung cũ trước
                    requests.append({
                        'deleteText': {
                            'objectId': element_id,
                            'textRange': {
                                'type': 'ALL'
                            }
                        }
                    })

                    # Thêm nội dung mới
                    requests.append({
                        'insertText': {
                            'objectId': element_id,
                            'text': clean_content,
                            'insertionIndex': 0
                        }
                    })

                    logger.debug(f"Updated element {element_id} with content: {clean_content[:50]}...")

                if action == 'update':
                    slides_updated += 1

            # Thực thi tất cả requests
            if requests:
                logger.info(f"Executing {len(requests)} batch update requests")

                # Chia requests thành các batch nhỏ để tránh timeout
                batch_size = 50
                for i in range(0, len(requests), batch_size):
                    batch_requests = requests[i:i + batch_size]
                    logger.info(f"Executing batch {i//batch_size + 1}: {len(batch_requests)} requests")

                    self.slides_service.presentations().batchUpdate(
                        presentationId=presentation_id,
                        body={'requests': batch_requests}
                    ).execute()

                logger.info("All batch updates completed successfully")
            else:
                logger.warning("No requests to execute")

            return {
                "success": True,
                "presentation_id": presentation_id,
                "slides_updated": slides_updated,
                "slides_created": slides_created,
                "total_slides_processed": len(slides_content),
                "requests_executed": len(requests)
            }

        except HttpError as e:
            logger.error(f"HTTP error creating slides for {presentation_id}: {e}")
            return {
                "success": False,
                "error": f"HTTP error: {e}"
            }
        except Exception as e:
            logger.error(f"Error creating slides for {presentation_id}: {e}")
            return {
                "success": False,
                "error": str(e)
            }


def get_google_slides_service() -> GoogleSlidesService:
    return GoogleSlidesService()
