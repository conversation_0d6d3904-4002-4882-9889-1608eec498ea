"""
Test sau khi share template với Service Account
"""

import requests
import json

def test_copy_after_share():
    """Test copy template sau khi share với Service Account"""
    template_id = "1hiKvx_qogkBh7jxfowHso9WYvA77NvFUpz3cRICzdxg"
    
    print("🔄 TESTING COPY AFTER SHARING WITH SERVICE ACCOUNT")
    print("=" * 60)
    print(f"Template ID: {template_id}")
    print(f"Service Account: <EMAIL>")
    print()
    print("📋 STEPS TO SHARE:")
    print("1. Open: https://docs.google.com/presentation/d/1hiKvx_qogkBh7jxfowHso9WYvA77NvFUpz3cRICzdxg/edit")
    print("2. Click 'Share' button")
    print("3. Add email: <EMAIL>")
    print("4. Set permission: Editor")
    print("5. Click 'Send'")
    print()
    
    input("Press Enter after sharing the template...")
    
    # Test copy và analyze
    payload = {
        "template_id": template_id,
        "new_title": "Test Copy After Share"
    }
    
    try:
        url = "http://localhost:8000/api/v1/slides/copy-and-analyze-template"
        response = requests.post(url, json=payload)
        
        print(f"📤 REQUEST:")
        print(f"URL: {url}")
        print(f"Payload: {json.dumps(payload, indent=2, ensure_ascii=False)}")
        print()
        
        print(f"📥 RESPONSE:")
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ COPY AND ANALYZE SUCCESS!")
            print(f"  Original Template ID: {result.get('original_template_id')}")
            print(f"  Copied Presentation ID: {result.get('copied_presentation_id')}")
            print(f"  Presentation Title: {result.get('presentation_title')}")
            print(f"  Web View Link: {result.get('web_view_link')}")
            print(f"  Slide Count: {result.get('slide_count')}")
            print()
            
            # Chi tiết về slides
            slides = result.get('slides', [])
            print(f"📊 SLIDES STRUCTURE:")
            for i, slide in enumerate(slides):
                print(f"  Slide {i+1} (ID: {slide['slideId']}):")
                elements = slide.get('elements', [])
                print(f"    Elements: {len(elements)}")
                for j, element in enumerate(elements):
                    text_preview = element['text'][:30] + "..." if len(element['text']) > 30 else element['text']
                    print(f"      {j+1}. {element['objectId']}: \"{text_preview}\"")
                print()
            
            print(f"🔗 OPEN COPIED PRESENTATION: {result.get('web_view_link')}")
            return result
            
        else:
            print("❌ COPY AND ANALYZE STILL FAILED!")
            try:
                error_detail = response.json()
                print(f"Error: {json.dumps(error_detail, indent=2, ensure_ascii=False)}")
                
                if "not found" in str(error_detail).lower():
                    print("\n💡 POSSIBLE ISSUES:")
                    print("1. Template chưa được share với Service Account")
                    print("2. Service Account chưa có quyền Editor")
                    print("3. Cần đợi vài phút để permission có hiệu lực")
                    
            except:
                print(f"Raw error: {response.text}")
            return None
                
    except Exception as e:
        print(f"❌ EXCEPTION: {e}")
        return None

if __name__ == "__main__":
    test_copy_after_share()
