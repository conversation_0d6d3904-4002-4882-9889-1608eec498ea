"""
Debug script để xem cấu trúc text và font size từ Google Slides API
"""

import json
import asyncio
from app.services.google_slides_service import get_google_slides_service

async def debug_text_structure():
    """Debug cấu trúc text và font size"""
    template_id = "19QE39G1EFZGdm-_m3TY1poD5v1we2T5Z_rglalK_4mA"
    
    print(f"🔍 Debugging Text Structure for template: {template_id}")
    print("=" * 80)
    
    # Get slides service
    slides_service = get_google_slides_service()
    
    if not slides_service.is_available():
        print("❌ Google Slides service not available")
        return
    
    try:
        # Get raw presentation data
        presentation = slides_service.slides_service.presentations().get(
            presentationId=template_id
        ).execute()
        
        slides = presentation.get('slides', [])
        if not slides:
            print("❌ No slides found")
            return
            
        # Analyze first slide text elements
        first_slide = slides[0]
        elements = first_slide.get('pageElements', [])
        
        print(f"📊 SLIDE: {first_slide.get('objectId')}")
        print(f"Elements count: {len(elements)}")
        print()
        
        for i, element in enumerate(elements):
            if 'shape' in element and 'text' in element['shape']:
                print(f"🔍 TEXT ELEMENT {i+1}:")
                print(f"Object ID: {element.get('objectId')}")
                
                shape = element['shape']
                text_content = shape['text']
                
                print(f"📋 TEXT STRUCTURE:")
                print(f"  Text keys: {list(text_content.keys())}")
                
                if 'textElements' in text_content:
                    text_elements = text_content['textElements']
                    print(f"  Text elements count: {len(text_elements)}")
                    
                    for j, text_elem in enumerate(text_elements):
                        print(f"  📝 TEXT ELEMENT {j+1}:")
                        print(f"    Keys: {list(text_elem.keys())}")
                        
                        # Check textRun for content and style
                        if 'textRun' in text_elem:
                            text_run = text_elem['textRun']
                            print(f"    Content: '{text_run.get('content', '').strip()}'")
                            print(f"    TextRun keys: {list(text_run.keys())}")
                            
                            # Check style for font information
                            if 'style' in text_run:
                                style = text_run['style']
                                print(f"    Style keys: {list(style.keys())}")
                                
                                # Look for font size
                                if 'fontSize' in style:
                                    font_size = style['fontSize']
                                    print(f"    Font Size: {font_size}")
                                
                                # Look for font family
                                if 'fontFamily' in style:
                                    font_family = style['fontFamily']
                                    print(f"    Font Family: {font_family}")
                                
                                # Look for other text properties
                                for key, value in style.items():
                                    if key not in ['fontSize', 'fontFamily']:
                                        print(f"    {key}: {value}")
                                
                                print(f"    Full Style: {json.dumps(style, indent=6)}")
                            else:
                                print(f"    Style: NOT FOUND")
                        
                        # Check paragraphMarker for paragraph-level styling
                        if 'paragraphMarker' in text_elem:
                            para_marker = text_elem['paragraphMarker']
                            print(f"    ParagraphMarker keys: {list(para_marker.keys())}")
                            
                            if 'style' in para_marker:
                                para_style = para_marker['style']
                                print(f"    Paragraph Style: {json.dumps(para_style, indent=6)}")
                        
                        print("-" * 40)
                
                print("=" * 60)
                print()
        
        # Save text structure for analysis
        text_debug_data = {
            "presentation_id": template_id,
            "text_elements": []
        }
        
        for element in elements:
            if 'shape' in element and 'text' in element['shape']:
                text_debug_data["text_elements"].append({
                    "objectId": element.get('objectId'),
                    "text_structure": element['shape']['text']
                })
        
        with open('text_structure_debug.json', 'w', encoding='utf-8') as f:
            json.dump(text_debug_data, f, indent=2, ensure_ascii=False)
        
        print("✅ Text structure saved to 'text_structure_debug.json'")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(debug_text_structure())
