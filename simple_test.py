"""
Simple test cho Slide Generation API
"""

import requests
import json
import time

def test_health_check():
    """Test health check"""
    print("=== Testing Health Check ===")

    url = "http://localhost:8000/api/v1/slides/health"

    try:
        response = requests.get(url)
        print(f"Status Code: {response.status_code}")

        if response.status_code == 200:
            result = response.json()
            print("✅ Health Check Success!")
            print(f"Status: {result.get('status')}")
            print(f"Services: {json.dumps(result.get('services', {}), indent=2)}")
        else:
            print("❌ Health Check Failed:")
            print(response.text)

    except Exception as e:
        print(f"❌ Exception: {e}")

    print()

def test_template_info():
    """Test template info với một template ID mẫu"""
    print("=== Testing Template Info ===")

    # Template ID từ request của bạn
    template_id = "1hiKvx_qogkBh7jxfowHso9WYvA77NvFUpz3cRICzdxg"

    print(f"Testing template info for: {template_id}")

    url = f"http://localhost:8000/api/v1/slides/template-info/{template_id}"

    try:
        response = requests.get(url)
        print(f"Status Code: {response.status_code}")

        if response.status_code == 200:
            result = response.json()
            print("✅ Template Analysis Success!")
            print(f"Template Title: {result.get('title')}")
            print(f"Layouts Count: {len(result.get('layouts', []))}")
            print(f"Slide Count: {result.get('slide_count')}")

            # Show layouts info
            for i, layout in enumerate(result.get('layouts', [])):
                print(f"\nLayout {i+1}:")
                print(f"  ID: {layout.get('layoutId')}")
                print(f"  Name: {layout.get('name')}")
                print(f"  Placeholders: {len(layout.get('placeholders', []))}")

        else:
            print("❌ Template Analysis Failed:")
            error_detail = response.json().get('detail', {})
            if isinstance(error_detail, dict):
                print(f"Error Code: {error_detail.get('error_code')}")
                print(f"Error Message: {error_detail.get('error_message')}")
            else:
                print(response.text)

    except Exception as e:
        print(f"❌ Exception: {e}")

    print()

def wait_for_api_enable():
    """Đợi API được enable và test lại"""
    print("=== Waiting for Google Slides API to be enabled ===")
    print("Please:")
    print("1. Go to: https://console.developers.google.com/apis/api/slides.googleapis.com/overview?project=141042596549")
    print("2. Click 'ENABLE' button")
    print("3. Wait a few minutes for the API to be activated")
    print("4. Press Enter to continue testing...")

    input()

    # Test lại sau khi enable
    max_retries = 5
    retry_delay = 30  # 30 seconds

    for attempt in range(max_retries):
        print(f"\n--- Attempt {attempt + 1}/{max_retries} ---")

        # Test health check
        test_health_check()

        # Test template info
        test_template_info()

        # Kiểm tra xem có thành công không
        url = f"http://localhost:8000/api/v1/slides/template-info/1hiKvx_qogkBh7jxfowHso9WYvA77NvFUpz3cRICzdxg"
        try:
            response = requests.get(url)
            if response.status_code == 200:
                print("🎉 Google Slides API is now working!")
                return True
            elif "SERVICE_DISABLED" not in response.text:
                print("🔄 API enabled but there might be other issues")
                return True
        except:
            pass

        if attempt < max_retries - 1:
            print(f"⏳ Waiting {retry_delay} seconds before next attempt...")
            time.sleep(retry_delay)

    print("❌ API still not working after all attempts")
    return False

if __name__ == "__main__":
    print("🚀 Google Slides API Test")
    print("=" * 50)

    # Test health check first
    test_health_check()

    # Test template info
    test_template_info()

    # Nếu API chưa enable, đợi user enable
    wait_for_api_enable()
