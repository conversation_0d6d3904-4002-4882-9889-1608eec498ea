"""
Debug script để xem toàn bộ cấu trúc element từ Google Slides API
"""

import json
import asyncio
from app.services.google_slides_service import get_google_slides_service

async def debug_element_structure():
    """Debug toàn bộ cấu trúc element"""
    template_id = "19QE39G1EFZGdm-_m3TY1poD5v1we2T5Z_rglalK_4mA"
    
    print(f"🔍 Debugging Element Structure for template: {template_id}")
    print("=" * 80)
    
    # Get slides service
    slides_service = get_google_slides_service()
    
    if not slides_service.is_available():
        print("❌ Google Slides service not available")
        return
    
    try:
        # Get raw presentation data
        presentation = slides_service.slides_service.presentations().get(
            presentationId=template_id
        ).execute()
        
        slides = presentation.get('slides', [])
        if not slides:
            print("❌ No slides found")
            return
            
        # Analyze first slide elements in detail
        first_slide = slides[0]
        elements = first_slide.get('pageElements', [])
        
        print(f"📊 SLIDE: {first_slide.get('objectId')}")
        print(f"Elements count: {len(elements)}")
        print()
        
        for i, element in enumerate(elements):
            print(f"🔍 ELEMENT {i+1} - COMPLETE STRUCTURE:")
            print(f"Object ID: {element.get('objectId')}")
            print()
            
            # Print all top-level keys
            print(f"Top-level keys: {list(element.keys())}")
            print()
            
            # Detailed analysis of each key
            for key, value in element.items():
                print(f"📋 {key.upper()}:")
                if isinstance(value, dict):
                    print(f"  Type: dict")
                    print(f"  Keys: {list(value.keys())}")
                    if key == 'size':
                        print(f"  Full size structure: {json.dumps(value, indent=4)}")
                    elif key == 'transform':
                        print(f"  Full transform structure: {json.dumps(value, indent=4)}")
                    elif key == 'shape':
                        print(f"  Shape type: {value.get('shapeType')}")
                        print(f"  Shape keys: {list(value.keys())}")
                        # Check if shape has additional size info
                        if 'shapeProperties' in value:
                            shape_props = value['shapeProperties']
                            print(f"  ShapeProperties keys: {list(shape_props.keys())}")
                            # Look for size-related properties
                            for prop_key, prop_value in shape_props.items():
                                if 'size' in prop_key.lower() or 'dimension' in prop_key.lower():
                                    print(f"    Size-related property {prop_key}: {prop_value}")
                else:
                    print(f"  Type: {type(value)}")
                    print(f"  Value: {value}")
                print()
            
            print("=" * 60)
            print()
        
        # Check if there are other places where size might be stored
        print("🔍 LOOKING FOR ALTERNATIVE SIZE SOURCES:")
        
        # Check layouts and masters for size information
        layouts = presentation.get('layouts', [])
        masters = presentation.get('masters', [])
        
        print(f"Layouts count: {len(layouts)}")
        print(f"Masters count: {len(masters)}")
        
        if layouts:
            first_layout = layouts[0]
            layout_elements = first_layout.get('pageElements', [])
            print(f"First layout elements: {len(layout_elements)}")
            
            for element in layout_elements[:2]:  # Check first 2 elements
                print(f"Layout element {element.get('objectId')}:")
                print(f"  Size: {element.get('size', 'NOT FOUND')}")
                print(f"  Transform: {element.get('transform', 'NOT FOUND')}")
        
        # Save complete structure for analysis
        debug_data = {
            "presentation_id": template_id,
            "slide_elements": elements,
            "layouts": layouts[:1] if layouts else [],  # First layout only
            "masters": masters[:1] if masters else []   # First master only
        }
        
        with open('complete_element_structure.json', 'w', encoding='utf-8') as f:
            json.dump(debug_data, f, indent=2, ensure_ascii=False)
        
        print("✅ Complete structure saved to 'complete_element_structure.json'")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(debug_element_structure())
