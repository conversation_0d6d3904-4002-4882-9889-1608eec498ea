"""
Test script để kiểm tra slide generation service sau khi fix
"""

import asyncio
import json
import requests
from datetime import datetime

# Configuration
BASE_URL = "http://localhost:8000/api/v1"
TEST_LESSON_ID = "1"  # User provided lesson ID
TEST_TEMPLATE_ID = "19QE39G1EFZGdm-_m3TY1poD5v1we2T5Z_rglalK_4mA"  # User provided template ID

async def test_slide_generation():
    """Test slide generation với error handling cải thiện"""
    print("🧪 TESTING SLIDE GENERATION WITH IMPROVED ERROR HANDLING")
    print("=" * 60)
    
    # Test data - User provided input
    payload = {
        "lesson_id": "1",
        "template_id": "19QE39G1EFZGdm-_m3TY1poD5v1we2T5Z_rglalK_4mA",
        "config_prompt": "Tạo slide với phong cách sinh động, phù hợp với học sinh",
        "presentation_title": "Slide CỦA đạt dep trai"
    }
    
    print(f"📝 Request payload:")
    print(json.dumps(payload, indent=2, ensure_ascii=False))
    print()
    
    try:
        print("🚀 Sending request to slide generation API...")
        response = requests.post(
            f"{BASE_URL}/slides/generate-slides",
            json=payload,
            timeout=120  # Increase timeout for LLM processing
        )
        
        print(f"📊 Response Status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ SLIDE GENERATION SUCCESSFUL!")
            print(f"📄 Response:")
            print(json.dumps(result, indent=2, ensure_ascii=False))
            
            # Check if fallback was used
            if result.get("fallback_used"):
                print("\n⚠️ Note: Fallback mechanism was used due to LLM issues")
            
        else:
            print("❌ SLIDE GENERATION FAILED!")
            try:
                error_detail = response.json()
                print(f"Error details:")
                print(json.dumps(error_detail, indent=2, ensure_ascii=False))
            except:
                print(f"Raw error response: {response.text}")
                
    except requests.exceptions.Timeout:
        print("❌ REQUEST TIMEOUT - LLM processing took too long")
    except Exception as e:
        print(f"❌ EXCEPTION: {e}")

def test_template_info():
    """Test template info để đảm bảo template accessible"""
    print("\n🔍 TESTING TEMPLATE INFO")
    print("=" * 40)
    
    try:
        response = requests.get(f"{BASE_URL}/slides/template-info/{TEST_TEMPLATE_ID}")
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Template accessible")
            print(f"Title: {result.get('title')}")
            print(f"Slides: {result.get('slide_count')}")
            print(f"Elements found: {sum(len(slide.get('elements', [])) for slide in result.get('slides', []))}")
        else:
            print(f"❌ Template error: {response.text}")
            
    except Exception as e:
        print(f"❌ Exception: {e}")

def test_llm_service():
    """Test LLM service availability"""
    print("\n🤖 TESTING LLM SERVICE")
    print("=" * 30)
    
    try:
        response = requests.get(f"{BASE_URL}/health")
        if response.status_code == 200:
            health = response.json()
            print("✅ API Health OK")
            
            # Check if we can get more specific LLM health info
            # This would need to be implemented in the API
            print("💡 Consider adding LLM-specific health check endpoint")
        else:
            print(f"❌ API Health issue: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Exception: {e}")

async def main():
    """Main test function"""
    print("🔧 SLIDE GENERATION FIX TESTING")
    print("=" * 50)
    print(f"Testing at: {datetime.now()}")
    print()
    
    # Test 1: Template accessibility
    test_template_info()
    
    # Test 2: LLM service
    test_llm_service()
    
    # Test 3: Full slide generation
    await test_slide_generation()
    
    print("\n" + "=" * 50)
    print("🏁 TESTING COMPLETED")
    
    print("\n💡 TROUBLESHOOTING TIPS:")
    print("1. Check if OpenRouter API key is set correctly")
    print("2. Verify template has public access permissions")
    print("3. Ensure lesson content exists in database")
    print("4. Check server logs for detailed error messages")

if __name__ == "__main__":
    asyncio.run(main())
