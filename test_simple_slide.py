"""
Simple test để kiểm tra slide generation với input của user
"""

import requests
import json

def test_slide_generation():
    """Test slide generation với input cụ thể"""
    
    url = "http://localhost:8000/api/v1/slides/generate-slides"
    
    payload = {
        "lesson_id": "1",
        "template_id": "19QE39G1EFZGdm-_m3TY1poD5v1we2T5Z_rglalK_4mA",
        "config_prompt": "Tạo slide với phong cách sinh động, phù hợp với học sinh",
        "presentation_title": "Slide CỦA đạt dep trai"
    }
    
    print("🚀 Testing slide generation...")
    print(f"📝 Payload: {json.dumps(payload, indent=2, ensure_ascii=False)}")
    print()
    
    try:
        response = requests.post(url, json=payload, timeout=120)
        
        print(f"📊 Status Code: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ SUCCESS!")
            print(f"🔗 Presentation Link: {result.get('web_view_link')}")
            print(f"📄 Presentation ID: {result.get('presentation_id')}")
            print(f"📊 Slides Created: {result.get('slides_created')}")
            print()
            print("📋 Full Response:")
            print(json.dumps(result, indent=2, ensure_ascii=False))
            
        else:
            print("❌ FAILED!")
            try:
                error = response.json()
                print(f"Error: {json.dumps(error, indent=2, ensure_ascii=False)}")
            except:
                print(f"Raw error: {response.text}")
                
    except Exception as e:
        print(f"❌ Exception: {e}")

if __name__ == "__main__":
    test_slide_generation()
