"""
Debug script để xem cấu trúc chi tiết của Google Slides template
"""

import requests
import json

def debug_template_structure():
    """Debug template structure chi tiết"""
    template_id = "1hiKvx_qogkBh7jxfowHso9WYvA77NvFUpz3cRICzdxg"
    
    print(f"🔍 Debugging template structure: {template_id}")
    print("=" * 60)
    
    # Gọi API để lấy thông tin template
    url = f"http://localhost:8000/api/v1/slides/template-info/{template_id}"
    
    try:
        response = requests.get(url)
        
        if response.status_code == 200:
            result = response.json()
            
            print("📊 TEMPLATE OVERVIEW:")
            print(f"  Title: {result.get('title')}")
            print(f"  Template ID: {result.get('template_id')}")
            print(f"  Slide Count: {result.get('slide_count')}")
            print(f"  Layouts Count: {len(result.get('layouts', []))}")
            print()
            
            # Chi tiết về layouts
            layouts = result.get('layouts', [])
            if layouts:
                print("🎨 LAYOUTS FOUND:")
                for i, layout in enumerate(layouts):
                    print(f"  Layout {i+1}:")
                    print(f"    ID: {layout.get('layoutId')}")
                    print(f"    Name: {layout.get('name')}")
                    print(f"    Placeholders: {len(layout.get('placeholders', []))}")
                    
                    # Chi tiết placeholders
                    placeholders = layout.get('placeholders', [])
                    if placeholders:
                        print(f"    📝 Placeholders:")
                        for j, placeholder in enumerate(placeholders):
                            print(f"      {j+1}. ID: {placeholder.get('objectId')}")
                            print(f"         Type: {placeholder.get('type')}")
                            print(f"         Index: {placeholder.get('index')}")
                    else:
                        print(f"    ❌ No placeholders found")
                    print()
            else:
                print("❌ NO LAYOUTS FOUND")
                print()
                
                # Gợi ý khắc phục
                print("💡 POSSIBLE SOLUTIONS:")
                print("1. Template có thể chỉ có text boxes thường, không phải placeholders")
                print("2. Cần tạo template với Master Slides có placeholders")
                print("3. Hoặc sử dụng template có sẵn từ Google Slides")
                print()
            
            # Hiển thị raw data để debug
            print("🔧 RAW RESPONSE DATA:")
            print(json.dumps(result, indent=2))
            
        else:
            print(f"❌ API Error: {response.status_code}")
            print(response.text)
            
    except Exception as e:
        print(f"❌ Exception: {e}")

def suggest_template_creation():
    """Gợi ý cách tạo template đúng"""
    print("\n" + "=" * 60)
    print("📝 HƯỚNG DẪN TẠO TEMPLATE ĐÚNG CÁCH:")
    print("=" * 60)
    
    print("""
1. 🎨 TẠO TEMPLATE MỚI:
   - Mở Google Slides
   - Tạo presentation mới
   - Vào View > Master để chỉnh sửa master slides

2. 📋 THÊM PLACEHOLDERS:
   - Trong Master view, thêm text boxes
   - Đặt tên cho các placeholder (Title, Content, etc.)
   - Sử dụng Insert > Placeholder để tạo placeholder thực sự

3. 🔗 CHIA SẺ TEMPLATE:
   - Share template với "Anyone with the link can view"
   - Copy link và lấy ID từ URL

4. 🧪 TEST TEMPLATE:
   - Sử dụng template ID mới trong API
   - Kiểm tra xem có layouts và placeholders không

5. 📚 SỬ DỤNG TEMPLATE CÓ SẴN:
   - Tìm template có sẵn trong Google Slides
   - Hoặc sử dụng template từ Google Slides Gallery
""")

if __name__ == "__main__":
    debug_template_structure()
    suggest_template_creation()
