"""
Test script để kiểm tra API template-info sau khi fix
"""

import requests
import json

def test_template_info_api():
    """Test template info API"""
    template_id = "19QE39G1EFZGdm-_m3TY1poD5v1we2T5Z_rglalK_4mA"
    url = f"http://localhost:8000/api/v1/slides/template-info/{template_id}"
    
    print(f"🔍 Testing Template Info API")
    print(f"URL: {url}")
    print("=" * 80)
    
    try:
        response = requests.get(url)
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            
            print("✅ API Response Success")
            print(f"Template ID: {result.get('template_id')}")
            print(f"Title: {result.get('title')}")
            print(f"Slide Count: {result.get('slide_count')}")
            print()
            
            # Check first slide elements
            slides = result.get('slides', [])
            if slides:
                first_slide = slides[0]
                print(f"🔍 FIRST SLIDE ANALYSIS:")
                print(f"  Slide ID: {first_slide.get('slideId')}")
                print(f"  Elements Count: {len(first_slide.get('elements', []))}")
                print()
                
                # Check each element
                for i, element in enumerate(first_slide.get('elements', [])):
                    print(f"  📋 ELEMENT {i+1}:")
                    print(f"    Object ID: {element.get('objectId')}")
                    print(f"    Text: '{element.get('text', '')}'")
                    
                    # Check size
                    size = element.get('size', {})
                    if size:
                        print(f"    Size (API): {size}")
                        if isinstance(size, dict) and 'width' in size:
                            width = size['width']
                            height = size['height']
                            print(f"      Width: {width}")
                            print(f"      Height: {height}")
                        else:
                            print(f"      Size structure: {type(size)}")
                    else:
                        print(f"    Size: EMPTY")

                    # Check actualSize
                    actual_size = element.get('actualSize', {})
                    if actual_size:
                        print(f"    ActualSize (Calculated): {actual_size}")
                        if isinstance(actual_size, dict) and 'width' in actual_size:
                            width = actual_size['width']
                            height = actual_size['height']
                            print(f"      Actual Width: {width}")
                            print(f"      Actual Height: {height}")
                    else:
                        print(f"    ActualSize: NOT AVAILABLE")

                    # Check textStyle
                    text_style = element.get('textStyle', {})
                    if text_style:
                        print(f"    TextStyle: {text_style}")
                        if 'fontSize' in text_style and text_style['fontSize']:
                            print(f"      Font Size: {text_style['fontSize']}")
                        if 'fontFamily' in text_style and text_style['fontFamily']:
                            print(f"      Font Family: {text_style['fontFamily']}")
                        if 'placeholder' in text_style and text_style['placeholder']:
                            placeholder = text_style['placeholder']
                            print(f"      Placeholder Type: {placeholder.get('type')}")
                            print(f"      Parent Object ID: {placeholder.get('parentObjectId')}")
                    else:
                        print(f"    TextStyle: NOT AVAILABLE")
                    
                    # Check transform
                    transform = element.get('transform', {})
                    if transform:
                        print(f"    Transform: {transform}")
                        if isinstance(transform, dict):
                            print(f"      ScaleX: {transform.get('scaleX')}")
                            print(f"      ScaleY: {transform.get('scaleY')}")
                            print(f"      TranslateX: {transform.get('translateX')}")
                            print(f"      TranslateY: {transform.get('translateY')}")
                            print(f"      Unit: {transform.get('unit')}")
                        else:
                            print(f"      Transform structure: {type(transform)}")
                    else:
                        print(f"    Transform: EMPTY")
                    
                    print("-" * 60)
            
            # Save full response for analysis
            with open('template_api_response.json', 'w', encoding='utf-8') as f:
                json.dump(result, f, indent=2, ensure_ascii=False)
            
            print("✅ Full API response saved to 'template_api_response.json'")
            
        else:
            print(f"❌ API Error: {response.status_code}")
            print(f"Response: {response.text}")
            
    except Exception as e:
        print(f"❌ Exception: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_template_info_api()
