"""
Test async slide generation API
"""

import requests
import json
import time

def test_async_slide_generation():
    """Test async slide generation API"""
    
    template_id = "1hiKvx_qogkBh7jxfowHso9WYvA77NvFUpz3cRICzdxg"
    lesson_id = "test_lesson_chemistry"
    
    print("🚀 TESTING ASYNC SLIDE GENERATION API")
    print("=" * 50)
    print(f"Template ID: {template_id}")
    print(f"Lesson ID: {lesson_id}")
    print()
    
    # Test async API
    payload = {
        "lesson_id": lesson_id,
        "template_id": template_id,
        "config_prompt": "Tạo slide về bài học Hóa học với phong cách sinh động",
        "presentation_title": "Test Async - Bài học Hóa học"
    }
    
    try:
        url = "http://localhost:8000/api/v1/slides/generate-slides-async"
        response = requests.post(url, json=payload)
        
        print(f"📤 REQUEST:")
        print(f"URL: {url}")
        print(f"Payload: {json.dumps(payload, indent=2, ensure_ascii=False)}")
        print()
        
        print(f"📥 RESPONSE:")
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ ASYNC API SUCCESS!")
            print(f"  Task ID: {result.get('task_id')}")
            print(f"  Status: {result.get('status')}")
            print(f"  Message: {result.get('message')}")
            
            task_id = result.get('task_id')
            if task_id:
                print(f"\n🔄 Monitoring task progress...")
                monitor_task_progress(task_id)
            
        else:
            print("❌ ASYNC API FAILED!")
            try:
                error_detail = response.json()
                print(f"Error: {json.dumps(error_detail, indent=2, ensure_ascii=False)}")
            except:
                print(f"Raw error: {response.text}")
                
    except Exception as e:
        print(f"❌ EXCEPTION: {e}")

def monitor_task_progress(task_id):
    """Monitor task progress"""
    print(f"📊 MONITORING TASK: {task_id}")
    print("-" * 40)
    
    max_attempts = 20  # 10 minutes with 30 second intervals
    attempt = 0
    
    while attempt < max_attempts:
        try:
            response = requests.get(f"http://localhost:8000/api/v1/tasks/{task_id}/status")
            
            if response.status_code == 200:
                result = response.json()
                status = result.get('status')
                progress = result.get('progress', 0)
                message = result.get('message', '')
                
                print(f"[{attempt+1:2d}] Status: {status:10s} Progress: {progress:3d}% - {message}")
                
                if status in ['COMPLETED', 'FAILED']:
                    if status == 'COMPLETED':
                        task_result = result.get('result', {})
                        print(f"\n✅ TASK COMPLETED SUCCESSFULLY!")
                        if task_result.get('web_view_link'):
                            print(f"🔗 Presentation Link: {task_result['web_view_link']}")
                        print(f"📊 Slides Created: {task_result.get('slides_created', 'Unknown')}")
                    else:
                        print(f"\n❌ TASK FAILED: {message}")
                    break
                    
            else:
                print(f"Error checking task status: {response.text}")
                
        except Exception as e:
            print(f"Error: {e}")
        
        attempt += 1
        time.sleep(30)  # Wait 30 seconds before next check
    
    if attempt >= max_attempts:
        print("⏰ Timeout waiting for task completion")

if __name__ == "__main__":
    test_async_slide_generation()
