"""
Slide Generation Service
Xử lý logic sinh nội dung slide từ lesson content và template structure sử dụng LLM
"""

import logging
import threading
import json
from typing import Dict, Any, List, Optional
from datetime import datetime

from app.services.llm_service import get_llm_service
from app.services.textbook_retrieval_service import TextbookRetrievalService
from app.services.google_slides_service import get_google_slides_service

logger = logging.getLogger(__name__)


class SlideGenerationService:
    """
    Service để sinh nội dung slide từ lesson content và template
    Singleton pattern với Lazy Initialization
    """

    _instance = None
    _lock = threading.Lock()

    def __new__(cls):
        """Singleton pattern implementation với thread-safe"""
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super(SlideGenerationService, cls).__new__(cls)
                    cls._instance._initialized = False
        return cls._instance

    def __init__(self):
        """Lazy initialization - chỉ khởi tạo một lần"""
        if self._initialized:
            return

        self.llm_service = None
        self.textbook_service = None
        self.slides_service = None
        self._service_initialized = False
        self._initialized = True

    def _ensure_service_initialized(self):
        """Ensure services are initialized"""
        if not self._service_initialized:
            logger.info("🔄 SlideGenerationService: First-time initialization triggered")
            self.llm_service = get_llm_service()
            self.textbook_service = TextbookRetrievalService()
            self.slides_service = get_google_slides_service()
            self._service_initialized = True
            logger.info("✅ SlideGenerationService: Initialization completed")

    def is_available(self) -> bool:
        """Kiểm tra service có sẵn sàng không"""
        self._ensure_service_initialized()
        return (self.llm_service and self.llm_service.is_available() and 
                self.slides_service and self.slides_service.is_available())

    async def generate_slides_from_lesson(
        self,
        lesson_id: str,
        template_id: str,
        config_prompt: Optional[str] = None,
        presentation_title: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Tạo slides từ lesson_id và template_id (QUY TRÌNH MỚI)

        Args:
            lesson_id: ID của bài học
            template_id: ID của Google Slides template
            config_prompt: Prompt cấu hình tùy chỉnh (optional)
            presentation_title: Tiêu đề presentation tùy chỉnh (optional)

        Returns:
            Dict chứa kết quả tạo slides
        """
        if not self.is_available():
            return {
                "success": False,
                "error": "Slide generation service not available"
            }

        try:
            logger.info(f"Starting NEW slide generation process for lesson {lesson_id} with template {template_id}")

            # Bước 1: Lấy nội dung bài học
            lesson_result = await self.textbook_service.get_lesson_content(lesson_id)
            if not lesson_result:
                return {
                    "success": False,
                    "error": f"Could not retrieve lesson content for {lesson_id}"
                }

            lesson_content = lesson_result.get("lesson_content", "")
            if not lesson_content:
                return {
                    "success": False,
                    "error": f"Empty lesson content for {lesson_id}"
                }

            # Bước 2: Copy template và phân tích cấu trúc của bản sao (QUY TRÌNH MỚI)
            new_title = presentation_title or f"Bài học {lesson_id} - {datetime.now().strftime('%Y%m%d_%H%M%S')}"
            copy_and_analyze_result = await self.slides_service.copy_and_analyze_template(template_id, new_title)
            if not copy_and_analyze_result["success"]:
                return {
                    "success": False,
                    "error": f"Could not copy and analyze template: {copy_and_analyze_result['error']}"
                }

            # Bước 3: Sinh nội dung slides bằng LLM với cấu trúc của bản sao
            slides_content = await self._generate_slides_content(
                lesson_content,
                copy_and_analyze_result,
                config_prompt
            )
            if not slides_content["success"]:
                return slides_content

            # Bước 4: Cập nhật nội dung vào bản sao đã tạo
            update_result = await self.slides_service.update_copied_presentation_content(
                copy_and_analyze_result["copied_presentation_id"],
                slides_content["slides"]
            )
            if not update_result["success"]:
                return {
                    "success": False,
                    "error": f"Could not update presentation content: {update_result['error']}"
                }

            return {
                "success": True,
                "lesson_id": lesson_id,
                "original_template_id": template_id,
                "presentation_id": copy_and_analyze_result["copied_presentation_id"],
                "presentation_title": copy_and_analyze_result["presentation_title"],
                "web_view_link": copy_and_analyze_result["web_view_link"],
                "slides_created": update_result.get("slides_updated", 0) + update_result.get("slides_created", 0),
                "template_info": {
                    "title": copy_and_analyze_result["presentation_title"],
                    "layouts_count": copy_and_analyze_result["slide_count"]
                }
            }

        except Exception as e:
            logger.error(f"Error generating slides: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    async def _generate_slides_content(
        self,
        lesson_content: str,
        copied_presentation_info: Dict[str, Any],
        config_prompt: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Sinh nội dung slides bằng LLM (với cấu trúc từ bản sao)

        Args:
            lesson_content: Nội dung bài học
            copied_presentation_info: Thông tin presentation đã copy và phân tích
            config_prompt: Prompt cấu hình tùy chỉnh

        Returns:
            Dict chứa nội dung slides đã sinh
        """
        try:
            # Tạo prompt cho LLM với thông tin từ bản sao
            prompt = self._create_slide_generation_prompt(
                lesson_content,
                copied_presentation_info,
                config_prompt
            )

            # Validate and log prompt info for debugging
            if not self._validate_prompt(prompt, lesson_content, copied_presentation_info):
                return {
                    "success": False,
                    "error": "Invalid prompt or input data"
                }

            logger.info(f"Generated prompt length: {len(prompt)} characters")
            logger.debug(f"Prompt preview: {prompt[:500]}...")
            logger.info(f"Lesson content length: {len(lesson_content)} characters")
            logger.info(f"Presentation slides count: {len(copied_presentation_info.get('slides', []))}")

            # Gọi LLM để sinh nội dung với retry logic
            max_retries = 3
            for attempt in range(max_retries):
                logger.info(f"LLM generation attempt {attempt + 1}/{max_retries}")

                llm_result = await self.llm_service._generate_content(prompt)

                if llm_result["success"] and llm_result.get("text") and llm_result["text"].strip():
                    logger.info(f"LLM generation successful on attempt {attempt + 1}")
                    break
                else:
                    logger.warning(f"LLM generation attempt {attempt + 1} failed: {llm_result.get('error', 'Empty response')}")
                    if attempt == max_retries - 1:
                        return {
                            "success": False,
                            "error": f"LLM generation failed after {max_retries} attempts: {llm_result.get('error', 'Empty response')}"
                        }

                    # Wait a bit before retry
                    import asyncio
                    await asyncio.sleep(1)

            # Parse kết quả từ LLM
            try:
                # Log raw LLM response for debugging
                logger.info(f"Raw LLM response length: {len(llm_result['text'])}")
                logger.debug(f"Raw LLM response: {llm_result['text'][:500]}...")

                # Check if response is empty
                if not llm_result["text"] or not llm_result["text"].strip():
                    logger.error("LLM returned empty response")
                    return {
                        "success": False,
                        "error": "LLM returned empty response"
                    }

                # Try to extract JSON from response (in case there's extra text)
                response_text = llm_result["text"].strip()

                # Look for JSON array in the response
                json_start = response_text.find('[')
                json_end = response_text.rfind(']') + 1

                if json_start != -1 and json_end > json_start:
                    json_text = response_text[json_start:json_end]
                    logger.debug(f"Extracted JSON: {json_text[:200]}...")
                else:
                    # If no array found, try the whole response
                    json_text = response_text

                slides_data = json.loads(json_text)
                if not isinstance(slides_data, list):
                    raise ValueError("LLM output must be a list of slides")

                return {
                    "success": True,
                    "slides": slides_data
                }

            except json.JSONDecodeError as e:
                logger.error(f"Failed to parse LLM output as JSON: {e}")
                logger.error(f"Raw LLM response: {llm_result['text'][:1000] if llm_result['text'] else 'EMPTY'}")

                # Try fallback: create basic slide structure
                logger.info("Attempting fallback slide generation...")
                fallback_slides = self._create_fallback_slides(
                    lesson_content,
                    copied_presentation_info
                )

                if fallback_slides:
                    logger.info("Fallback slide generation successful")
                    return {
                        "success": True,
                        "slides": fallback_slides,
                        "fallback_used": True
                    }

                return {
                    "success": False,
                    "error": f"Invalid JSON from LLM and fallback failed: {e}"
                }

        except Exception as e:
            logger.error(f"Error generating slides content: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    def _create_slide_generation_prompt(
        self,
        lesson_content: str,
        copied_presentation_info: Dict[str, Any],
        config_prompt: Optional[str] = None
    ) -> str:
        """
        Tạo prompt cho LLM để sinh nội dung slides (với thông tin từ bản sao)

        Args:
            lesson_content: Nội dung bài học
            copied_presentation_info: Thông tin presentation đã copy
            config_prompt: Prompt cấu hình tùy chỉnh

        Returns:
            String prompt cho LLM
        """
        # Prompt cấu hình mặc định
        default_config = """
Bạn là chuyên gia thiết kế slide giáo dục. Hãy phân tích nội dung bài học và tạo slides đẹp, chuyên nghiệp.

NGUYÊN TẮC THIẾT KẾ:
1. MỖI SLIDE CHỈ CHỨA 1 Ý CHÍNH - không nhồi nhét quá nhiều thông tin
2. NỘI DUNG NGẮN GỌN - mỗi bullet point không quá 1-2 dòng
3. CÂN BẰNG THÔNG TIN - phân bổ đều nội dung giữa các slide
4. LOGIC TRÌNH BÀY - từ tổng quan đến chi tiết
5. DỄ ĐỌC - font chữ rõ ràng, không quá dài

YÊU CẦU CỤ THỂ:
- Tự động tạo thêm slides nếu nội dung nhiều (không bắt buộc đúng số slide template)
- Mỗi slide tối đa 3-4 bullet points
- Tiêu đề slide ngắn gọn, súc tích
- Nội dung chi tiết chia đều giữa các slide
- Tránh text dài, ưu tiên bullet points
- Đảm bảo tính giáo dục và dễ hiểu cho học sinh
"""

        # Sử dụng config_prompt nếu có, nếu không dùng mặc định
        final_config = config_prompt if config_prompt else default_config

        # Tạo thông tin chi tiết về slides và elements từ bản sao
        slides_info = "CẤU TRÚC PRESENTATION HIỆN TẠI:\n"
        slides_info += f"Tổng số slides: {len(copied_presentation_info.get('slides', []))}\n\n"

        for i, slide in enumerate(copied_presentation_info.get("slides", [])):
            slides_info += f"SLIDE {i+1} (ID: {slide['slideId']}):\n"
            elements = slide.get("elements", [])

            if not elements:
                slides_info += "  - Không có elements có thể chỉnh sửa\n"
            else:
                slides_info += f"  - Có {len(elements)} elements có thể chỉnh sửa:\n"
                for j, element in enumerate(elements):
                    element_text = element.get('text', '').strip()
                    element_preview = element_text[:30] + "..." if len(element_text) > 30 else element_text
                    slides_info += f"    {j+1}. {element['objectId']}: \"{element_preview}\"\n"
            slides_info += "\n"

        prompt = f"""
{final_config}

THÔNG TIN PRESENTATION ĐÃ COPY:
Tiêu đề: {copied_presentation_info.get('presentation_title', 'Không có tiêu đề')}
Số slide hiện tại: {copied_presentation_info.get('slide_count', 0)}
Presentation ID: {copied_presentation_info.get('copied_presentation_id')}

{slides_info}

NỘI DUNG BÀI HỌC:
{lesson_content}

HƯỚNG DẪN TẠO SLIDES:

1. PHÂN TÍCH NỘI DUNG: Chia nội dung bài học thành các phần logic
2. THIẾT KẾ SLIDES:
   - Slide 1: Tiêu đề chính + giới thiệu tổng quan
   - Slides tiếp theo: Từng phần nội dung chi tiết
   - Mỗi slide chỉ 1 chủ đề chính
   - Bullet points ngắn gọn (tối đa 3-4 points/slide)

3. TẠO THÊM SLIDES NẾU CẦN:
   - Nếu nội dung nhiều, tạo thêm slides bằng cách duplicate slide cuối
   - Sử dụng slideId mới: "new_slide_1", "new_slide_2", etc.
   - Copy cấu trúc elements từ slide tương tự

YÊU CẦU OUTPUT:
QUAN TRỌNG: Chỉ trả về JSON array, không có text khác. Format:

[
  {{
    "slideId": "slide_id_from_presentation_or_new_slide_X",
    "action": "update",
    "updates": {{
      "element_object_id": "Nội dung ngắn gọn, rõ ràng",
      "another_element_id": "• Bullet point 1\\n• Bullet point 2"
    }}
  }},
  {{
    "slideId": "new_slide_1",
    "action": "create",
    "baseSlideId": "slide_id_to_copy_from",
    "updates": {{
      "element_object_id": "Nội dung cho slide mới"
    }}
  }}
]

QUY TẮC:
- JSON array hợp lệ, bắt đầu [ kết thúc ]
- action: "update" (slide có sẵn) hoặc "create" (slide mới)
- Nội dung ngắn gọn, mỗi dòng tối đa 60 ký tự
- Sử dụng \\n để xuống dòng trong bullet points
- KHÔNG text giải thích ngoài JSON
"""

        return prompt

    def _validate_prompt(
        self,
        prompt: str,
        lesson_content: str,
        copied_presentation_info: Dict[str, Any]
    ) -> bool:
        """
        Validate prompt and input data before sending to LLM

        Args:
            prompt: Generated prompt
            lesson_content: Lesson content
            copied_presentation_info: Presentation info

        Returns:
            bool: True if valid, False otherwise
        """
        try:
            # Check prompt length (not too short or too long)
            if len(prompt) < 100:
                logger.error("Prompt too short")
                return False

            if len(prompt) > 50000:  # Reasonable limit for most LLMs
                logger.error(f"Prompt too long: {len(prompt)} characters")
                return False

            # Check lesson content
            if not lesson_content or len(lesson_content.strip()) < 10:
                logger.error("Lesson content too short or empty")
                return False

            # Check presentation info
            if not copied_presentation_info:
                logger.error("No presentation info provided")
                return False

            slides = copied_presentation_info.get("slides", [])
            if not slides:
                logger.error("No slides found in presentation info")
                return False

            # Check if slides have editable elements
            total_elements = sum(len(slide.get("elements", [])) for slide in slides)
            if total_elements == 0:
                logger.error("No editable elements found in slides")
                return False

            logger.info(f"Prompt validation passed: {len(slides)} slides, {total_elements} elements")
            return True

        except Exception as e:
            logger.error(f"Error validating prompt: {e}")
            return False

    def _create_fallback_slides(
        self,
        lesson_content: str,
        copied_presentation_info: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """
        Tạo slides cơ bản khi LLM fail (fallback mechanism)

        Args:
            lesson_content: Nội dung bài học
            copied_presentation_info: Thông tin presentation đã copy

        Returns:
            List các slide với nội dung cơ bản
        """
        try:
            fallback_slides = []
            slides_info = copied_presentation_info.get("slides", [])

            # Chia lesson content thành các phần
            content_parts = self._split_lesson_content(lesson_content)

            for i, slide_info in enumerate(slides_info):
                slide_id = slide_info.get("slideId")
                elements = slide_info.get("elements", [])

                if not elements:
                    continue

                updates = {}

                # Lấy nội dung tương ứng với slide
                content_part = content_parts[i] if i < len(content_parts) else content_parts[-1] if content_parts else lesson_content[:200]

                # Cập nhật từng element với nội dung cơ bản
                for j, element in enumerate(elements):
                    object_id = element.get("objectId")
                    if object_id:
                        if j == 0:  # Element đầu tiên thường là title
                            updates[object_id] = f"Slide {i+1}: {content_part[:50]}..."
                        else:  # Các element khác
                            updates[object_id] = content_part[:200] + "..." if len(content_part) > 200 else content_part

                if updates:
                    fallback_slides.append({
                        "slideId": slide_id,
                        "updates": updates
                    })

            return fallback_slides

        except Exception as e:
            logger.error(f"Error creating fallback slides: {e}")
            return []

    def _split_lesson_content(self, lesson_content: str) -> List[str]:
        """
        Chia lesson content thành các phần cho từng slide

        Args:
            lesson_content: Nội dung bài học

        Returns:
            List các phần nội dung
        """
        try:
            # Chia theo paragraph hoặc section
            parts = []

            # Thử chia theo dấu xuống dòng kép
            sections = lesson_content.split('\n\n')
            if len(sections) > 1:
                parts = [section.strip() for section in sections if section.strip()]
            else:
                # Chia theo dấu chấm
                sentences = lesson_content.split('. ')
                # Nhóm 2-3 câu thành một phần
                for i in range(0, len(sentences), 3):
                    part = '. '.join(sentences[i:i+3])
                    if part:
                        parts.append(part)

            # Đảm bảo có ít nhất 1 phần
            if not parts:
                parts = [lesson_content]

            return parts

        except Exception as e:
            logger.error(f"Error splitting lesson content: {e}")
            return [lesson_content]


# Hàm để lấy singleton instance
def get_slide_generation_service() -> SlideGenerationService:
    """
    Lấy singleton instance của SlideGenerationService
    Thread-safe lazy initialization

    Returns:
        SlideGenerationService: Singleton instance
    """
    return SlideGenerationService()
