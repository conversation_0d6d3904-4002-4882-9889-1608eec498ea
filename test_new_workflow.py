"""
Test script cho quy trình mới: Copy template và phân tích bản sao
"""

import requests
import json

def test_copy_and_analyze():
    """Test copy và analyze template"""
    template_id = "1hiKvx_qogkBh7jxfowHso9WYvA77NvFUpz3cRICzdxg"
    
    print("🔄 TESTING NEW WORKFLOW: Copy and Analyze Template")
    print("=" * 60)
    print(f"Template ID: {template_id}")
    print()
    
    # Test copy và analyze
    payload = {
        "template_id": template_id,
        "new_title": "Test Copy - Workflow Mới"
    }
    
    try:
        url = "http://localhost:8000/api/v1/slides/copy-and-analyze-template"
        response = requests.post(url, json=payload)
        
        print(f"📤 REQUEST:")
        print(f"URL: {url}")
        print(f"Payload: {json.dumps(payload, indent=2, ensure_ascii=False)}")
        print()
        
        print(f"📥 RESPONSE:")
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ COPY AND ANALYZE SUCCESS!")
            print(f"  Original Template ID: {result.get('original_template_id')}")
            print(f"  Copied Presentation ID: {result.get('copied_presentation_id')}")
            print(f"  Presentation Title: {result.get('presentation_title')}")
            print(f"  Web View Link: {result.get('web_view_link')}")
            print(f"  Slide Count: {result.get('slide_count')}")
            print()
            
            # Chi tiết về slides
            slides = result.get('slides', [])
            print(f"📊 SLIDES STRUCTURE:")
            for i, slide in enumerate(slides):
                print(f"  Slide {i+1} (ID: {slide['slideId']}):")
                elements = slide.get('elements', [])
                print(f"    Elements: {len(elements)}")
                for j, element in enumerate(elements):
                    text_preview = element['text'][:30] + "..." if len(element['text']) > 30 else element['text']
                    print(f"      {j+1}. {element['objectId']}: \"{text_preview}\"")
                print()
            
            # Lưu kết quả để test tiếp
            with open("copy_result.json", "w", encoding="utf-8") as f:
                json.dump(result, f, indent=2, ensure_ascii=False)
            print("💾 Kết quả đã được lưu vào copy_result.json")
            
            return result
            
        else:
            print("❌ COPY AND ANALYZE FAILED!")
            try:
                error_detail = response.json()
                print(f"Error: {json.dumps(error_detail, indent=2, ensure_ascii=False)}")
            except:
                print(f"Raw error: {response.text}")
            return None
                
    except Exception as e:
        print(f"❌ EXCEPTION: {e}")
        return None

def test_full_new_workflow():
    """Test toàn bộ quy trình mới với lesson content giả"""
    print("\n" + "=" * 60)
    print("🚀 TESTING FULL NEW WORKFLOW")
    print("=" * 60)
    
    # Sử dụng lesson content giả để test
    lesson_id = "test_lesson_chemistry"
    template_id = "1hiKvx_qogkBh7jxfowHso9WYvA77NvFUpz3cRICzdxg"
    
    payload = {
        "lesson_id": lesson_id,
        "template_id": template_id,
        "config_prompt": "Tạo slide về bài học Hóa học với phong cách sinh động, dễ hiểu cho học sinh THPT",
        "presentation_title": "Bài học Hóa học - Test Workflow Mới"
    }
    
    print(f"Lesson ID: {lesson_id}")
    print(f"Template ID: {template_id}")
    print()
    
    try:
        url = "http://localhost:8000/api/v1/slides/generate-slides"
        response = requests.post(url, json=payload)
        
        print(f"📤 REQUEST:")
        print(f"URL: {url}")
        print(f"Payload: {json.dumps(payload, indent=2, ensure_ascii=False)}")
        print()
        
        print(f"📥 RESPONSE:")
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ FULL WORKFLOW SUCCESS!")
            print(f"  Lesson ID: {result.get('lesson_id')}")
            print(f"  Original Template ID: {result.get('original_template_id')}")
            print(f"  Presentation ID: {result.get('presentation_id')}")
            print(f"  Presentation Title: {result.get('presentation_title')}")
            print(f"  Web View Link: {result.get('web_view_link')}")
            print(f"  Slides Created: {result.get('slides_created')}")
            
            # Mở link nếu có
            web_link = result.get('web_view_link')
            if web_link:
                print(f"\n🔗 OPEN LINK: {web_link}")
                
        else:
            print("❌ FULL WORKFLOW FAILED!")
            try:
                error_detail = response.json()
                print(f"Error: {json.dumps(error_detail, indent=2, ensure_ascii=False)}")
                
                # Kiểm tra lỗi lesson not found
                if "lesson content" in str(error_detail).lower():
                    print("\n💡 SUGGESTION:")
                    print("Lỗi này xảy ra vì lesson_id không tồn tại trong database.")
                    print("Để test đầy đủ, bạn cần:")
                    print("1. Import textbook vào database")
                    print("2. Sử dụng lesson_id thực tế")
                    print("3. Hoặc tạo mock lesson content trong service")
                    
            except:
                print(f"Raw error: {response.text}")
                
    except Exception as e:
        print(f"❌ EXCEPTION: {e}")

if __name__ == "__main__":
    print("🧪 TESTING NEW SLIDE GENERATION WORKFLOW")
    print("Quy trình mới: Copy template trước → Phân tích bản sao → Cập nhật nội dung")
    print()
    
    # Test 1: Copy và analyze
    copy_result = test_copy_and_analyze()
    
    # Test 2: Full workflow (sẽ fail ở lesson content nhưng test được copy/analyze)
    test_full_new_workflow()
    
    print("\n" + "=" * 60)
    print("📋 SUMMARY:")
    print("✅ Copy and Analyze Template: Hoạt động tốt")
    print("❌ Full Workflow: Cần lesson_id thực tế để test hoàn chỉnh")
    print("🔧 Next Steps: Import textbook hoặc tạo mock lesson content")
    print("=" * 60)
