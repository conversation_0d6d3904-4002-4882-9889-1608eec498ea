{"presentation_id": "19QE39G1EFZGdm-_m3TY1poD5v1we2T5Z_rglalK_4mA", "slide_elements": [{"objectId": "gc6f919934_0_1", "size": {"width": {"magnitude": 3000000, "unit": "EMU"}, "height": {"magnitude": 3000000, "unit": "EMU"}}, "transform": {"scaleX": 2.7407, "scaleY": 0.3112, "translateX": 390525, "translateY": 1819275, "unit": "EMU"}, "shape": {"shapeType": "TEXT_BOX", "text": {"textElements": [{"endIndex": 13, "paragraphMarker": {"style": {"direction": "LEFT_TO_RIGHT"}}}, {"endIndex": 13, "textRun": {"content": "Lesson Title\n", "style": {}}}]}, "shapeProperties": {"shapeBackgroundFill": {"propertyState": "INHERIT"}, "outline": {"propertyState": "INHERIT"}, "shadow": {"propertyState": "INHERIT"}, "autofit": {"fontScale": 1}}, "placeholder": {"type": "CENTERED_TITLE", "parentObjectId": "gc6f919934_0_70"}}}, {"objectId": "gc6f919934_0_2", "size": {"width": {"magnitude": 3000000, "unit": "EMU"}, "height": {"magnitude": 3000000, "unit": "EMU"}}, "transform": {"scaleX": 2.7407000064849854, "scaleY": 0.14429999887943268, "translateX": 390525, "translateY": 2789130.2734375, "unit": "EMU"}, "shape": {"shapeType": "TEXT_BOX", "text": {"textElements": [{"endIndex": 13, "paragraphMarker": {"style": {"direction": "LEFT_TO_RIGHT"}}}, {"endIndex": 13, "textRun": {"content": "Created Date\n", "style": {}}}]}, "shapeProperties": {"shapeBackgroundFill": {"propertyState": "INHERIT"}, "outline": {"propertyState": "INHERIT"}, "shadow": {"propertyState": "INHERIT"}, "autofit": {"fontScale": 1}}, "placeholder": {"type": "SUBTITLE", "parentObjectId": "gc6f919934_0_71"}}}], "layouts": [{"objectId": "gc6f919934_0_67", "pageType": "LAYOUT", "pageElements": [{"objectId": "gc6f919934_0_68", "size": {"width": {"magnitude": 3000000, "unit": "EMU"}, "height": {"magnitude": 3000000, "unit": "EMU"}}, "transform": {"scaleX": -0.29919999837875366, "scaleY": 0.29919999837875366, "translateX": 9144000, "translateY": 4245925, "unit": "EMU"}, "shape": {"shapeType": "RIGHT_TRIANGLE", "shapeProperties": {"shapeBackgroundFill": {"solidFill": {"color": {"themeColor": "LIGHT1"}, "alpha": 1}}, "outline": {"outlineFill": {"solidFill": {"color": {"rgbColor": {}}, "alpha": 1}}, "weight": {"magnitude": 9525, "unit": "EMU"}, "dashStyle": "SOLID", "propertyState": "NOT_RENDERED"}, "shadow": {"type": "OUTER", "transform": {"scaleX": 1, "scaleY": 1, "unit": "EMU"}, "alignment": "BOTTOM_LEFT", "blurRadius": {"unit": "EMU"}, "color": {"rgbColor": {}}, "alpha": 1, "rotateWithShape": false, "propertyState": "NOT_RENDERED"}, "contentAlignment": "MIDDLE", "autofit": {"autofitType": "NONE", "fontScale": 1}}}}, {"objectId": "gc6f919934_0_69", "size": {"width": {"magnitude": 3000000, "unit": "EMU"}, "height": {"magnitude": 3000000, "unit": "EMU"}}, "transform": {"scaleX": -0.29919999837875366, "scaleY": 0.29919999837875366, "translateX": 9144000, "translateY": 4245875, "unit": "EMU"}, "shape": {"shapeType": "ROUND_1_RECTANGLE", "shapeProperties": {"shapeBackgroundFill": {"solidFill": {"color": {"themeColor": "LIGHT1"}, "alpha": 0.6808}}, "outline": {"outlineFill": {"solidFill": {"color": {"rgbColor": {}}, "alpha": 1}}, "weight": {"magnitude": 9525, "unit": "EMU"}, "dashStyle": "SOLID", "propertyState": "NOT_RENDERED"}, "shadow": {"type": "OUTER", "transform": {"scaleX": 1, "scaleY": 1, "unit": "EMU"}, "alignment": "BOTTOM_LEFT", "blurRadius": {"unit": "EMU"}, "color": {"rgbColor": {}}, "alpha": 1, "rotateWithShape": false, "propertyState": "NOT_RENDERED"}, "contentAlignment": "MIDDLE", "autofit": {"autofitType": "NONE", "fontScale": 1}}}}, {"objectId": "gc6f919934_0_70", "size": {"width": {"magnitude": 3000000, "unit": "EMU"}, "height": {"magnitude": 3000000, "unit": "EMU"}}, "transform": {"scaleX": 2.7407000064849854, "scaleY": 0.31119999289512634, "translateX": 390525, "translateY": 1819275, "unit": "EMU"}, "shape": {"shapeType": "TEXT_BOX", "text": {"textElements": [{"endIndex": 1, "paragraphMarker": {"style": {"direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "glyph": " ", "bulletStyle": {}}}}, {"endIndex": 1, "textRun": {"content": "\n", "style": {"fontSize": {"magnitude": 48, "unit": "PT"}}}}, {"startIndex": 1, "endIndex": 2, "paragraphMarker": {"style": {"direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 1, "glyph": " ", "bulletStyle": {}}}}, {"startIndex": 1, "endIndex": 2, "textRun": {"content": "\n", "style": {"fontSize": {"magnitude": 48, "unit": "PT"}}}}, {"startIndex": 2, "endIndex": 3, "paragraphMarker": {"style": {"direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 2, "glyph": " ", "bulletStyle": {}}}}, {"startIndex": 2, "endIndex": 3, "textRun": {"content": "\n", "style": {"fontSize": {"magnitude": 48, "unit": "PT"}}}}, {"startIndex": 3, "endIndex": 4, "paragraphMarker": {"style": {"direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 3, "glyph": " ", "bulletStyle": {}}}}, {"startIndex": 3, "endIndex": 4, "textRun": {"content": "\n", "style": {"fontSize": {"magnitude": 48, "unit": "PT"}}}}, {"startIndex": 4, "endIndex": 5, "paragraphMarker": {"style": {"direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 4, "glyph": " ", "bulletStyle": {}}}}, {"startIndex": 4, "endIndex": 5, "textRun": {"content": "\n", "style": {"fontSize": {"magnitude": 48, "unit": "PT"}}}}, {"startIndex": 5, "endIndex": 6, "paragraphMarker": {"style": {"direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 5, "glyph": " ", "bulletStyle": {}}}}, {"startIndex": 5, "endIndex": 6, "textRun": {"content": "\n", "style": {"fontSize": {"magnitude": 48, "unit": "PT"}}}}, {"startIndex": 6, "endIndex": 7, "paragraphMarker": {"style": {"direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 6, "glyph": " ", "bulletStyle": {}}}}, {"startIndex": 6, "endIndex": 7, "textRun": {"content": "\n", "style": {"fontSize": {"magnitude": 48, "unit": "PT"}}}}, {"startIndex": 7, "endIndex": 8, "paragraphMarker": {"style": {"direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 7, "glyph": " ", "bulletStyle": {}}}}, {"startIndex": 7, "endIndex": 8, "textRun": {"content": "\n", "style": {"fontSize": {"magnitude": 48, "unit": "PT"}}}}, {"startIndex": 8, "endIndex": 9, "paragraphMarker": {"style": {"direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 8, "glyph": " ", "bulletStyle": {}}}}, {"startIndex": 8, "endIndex": 9, "textRun": {"content": "\n", "style": {"fontSize": {"magnitude": 48, "unit": "PT"}}}}], "lists": {"bodyPlaceholderListEntity": {"listId": "bodyPlaceholderListEntity", "nestingLevel": {"0": {"bulletStyle": {"fontSize": {"magnitude": 48, "unit": "PT"}}}, "1": {"bulletStyle": {"fontSize": {"magnitude": 48, "unit": "PT"}}}, "2": {"bulletStyle": {"fontSize": {"magnitude": 48, "unit": "PT"}}}, "3": {"bulletStyle": {"fontSize": {"magnitude": 48, "unit": "PT"}}}, "4": {"bulletStyle": {"fontSize": {"magnitude": 48, "unit": "PT"}}}, "5": {"bulletStyle": {"fontSize": {"magnitude": 48, "unit": "PT"}}}, "6": {"bulletStyle": {"fontSize": {"magnitude": 48, "unit": "PT"}}}, "7": {"bulletStyle": {"fontSize": {"magnitude": 48, "unit": "PT"}}}, "8": {"bulletStyle": {"fontSize": {"magnitude": 48, "unit": "PT"}}}}}}}, "shapeProperties": {"shapeBackgroundFill": {"propertyState": "INHERIT"}, "outline": {"propertyState": "INHERIT"}, "shadow": {"propertyState": "INHERIT"}, "autofit": {"fontScale": 1}}, "placeholder": {"type": "CENTERED_TITLE", "parentObjectId": "gc6f919934_0_64"}}}, {"objectId": "gc6f919934_0_71", "size": {"width": {"magnitude": 3000000, "unit": "EMU"}, "height": {"magnitude": 3000000, "unit": "EMU"}}, "transform": {"scaleX": 2.7407000064849854, "scaleY": 0.14429999887943268, "translateX": 390525, "translateY": 2789130.2734375, "unit": "EMU"}, "shape": {"shapeType": "TEXT_BOX", "text": {"textElements": [{"endIndex": 1, "paragraphMarker": {"style": {"lineSpacing": 100, "spaceBelow": {"unit": "PT"}, "direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "glyph": " ", "bulletStyle": {}}}}, {"endIndex": 1, "textRun": {"content": "\n", "style": {"foregroundColor": {"opaqueColor": {"themeColor": "LIGHT1"}}}}}, {"startIndex": 1, "endIndex": 2, "paragraphMarker": {"style": {"lineSpacing": 100, "spaceBelow": {"unit": "PT"}, "direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 1, "glyph": " ", "bulletStyle": {}}}}, {"startIndex": 1, "endIndex": 2, "textRun": {"content": "\n", "style": {"foregroundColor": {"opaqueColor": {"themeColor": "LIGHT1"}}, "fontSize": {"magnitude": 18, "unit": "PT"}}}}, {"startIndex": 2, "endIndex": 3, "paragraphMarker": {"style": {"lineSpacing": 100, "spaceBelow": {"unit": "PT"}, "direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 2, "glyph": " ", "bulletStyle": {}}}}, {"startIndex": 2, "endIndex": 3, "textRun": {"content": "\n", "style": {"foregroundColor": {"opaqueColor": {"themeColor": "LIGHT1"}}, "fontSize": {"magnitude": 18, "unit": "PT"}}}}, {"startIndex": 3, "endIndex": 4, "paragraphMarker": {"style": {"lineSpacing": 100, "spaceBelow": {"unit": "PT"}, "direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 3, "glyph": " ", "bulletStyle": {}}}}, {"startIndex": 3, "endIndex": 4, "textRun": {"content": "\n", "style": {"foregroundColor": {"opaqueColor": {"themeColor": "LIGHT1"}}, "fontSize": {"magnitude": 18, "unit": "PT"}}}}, {"startIndex": 4, "endIndex": 5, "paragraphMarker": {"style": {"lineSpacing": 100, "spaceBelow": {"unit": "PT"}, "direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 4, "glyph": " ", "bulletStyle": {}}}}, {"startIndex": 4, "endIndex": 5, "textRun": {"content": "\n", "style": {"foregroundColor": {"opaqueColor": {"themeColor": "LIGHT1"}}, "fontSize": {"magnitude": 18, "unit": "PT"}}}}, {"startIndex": 5, "endIndex": 6, "paragraphMarker": {"style": {"lineSpacing": 100, "spaceBelow": {"unit": "PT"}, "direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 5, "glyph": " ", "bulletStyle": {}}}}, {"startIndex": 5, "endIndex": 6, "textRun": {"content": "\n", "style": {"foregroundColor": {"opaqueColor": {"themeColor": "LIGHT1"}}, "fontSize": {"magnitude": 18, "unit": "PT"}}}}, {"startIndex": 6, "endIndex": 7, "paragraphMarker": {"style": {"lineSpacing": 100, "spaceBelow": {"unit": "PT"}, "direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 6, "glyph": " ", "bulletStyle": {}}}}, {"startIndex": 6, "endIndex": 7, "textRun": {"content": "\n", "style": {"foregroundColor": {"opaqueColor": {"themeColor": "LIGHT1"}}, "fontSize": {"magnitude": 18, "unit": "PT"}}}}, {"startIndex": 7, "endIndex": 8, "paragraphMarker": {"style": {"lineSpacing": 100, "spaceBelow": {"unit": "PT"}, "direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 7, "glyph": " ", "bulletStyle": {}}}}, {"startIndex": 7, "endIndex": 8, "textRun": {"content": "\n", "style": {"foregroundColor": {"opaqueColor": {"themeColor": "LIGHT1"}}, "fontSize": {"magnitude": 18, "unit": "PT"}}}}, {"startIndex": 8, "endIndex": 9, "paragraphMarker": {"style": {"lineSpacing": 100, "spaceBelow": {"unit": "PT"}, "direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 8, "glyph": " ", "bulletStyle": {}}}}, {"startIndex": 8, "endIndex": 9, "textRun": {"content": "\n", "style": {"foregroundColor": {"opaqueColor": {"themeColor": "LIGHT1"}}, "fontSize": {"magnitude": 18, "unit": "PT"}}}}], "lists": {"bodyPlaceholderListEntity": {"listId": "bodyPlaceholderListEntity", "nestingLevel": {"0": {"bulletStyle": {"foregroundColor": {"opaqueColor": {"themeColor": "LIGHT1"}}}}, "1": {"bulletStyle": {"foregroundColor": {"opaqueColor": {"themeColor": "LIGHT1"}}, "fontSize": {"magnitude": 18, "unit": "PT"}}}, "2": {"bulletStyle": {"foregroundColor": {"opaqueColor": {"themeColor": "LIGHT1"}}, "fontSize": {"magnitude": 18, "unit": "PT"}}}, "3": {"bulletStyle": {"foregroundColor": {"opaqueColor": {"themeColor": "LIGHT1"}}, "fontSize": {"magnitude": 18, "unit": "PT"}}}, "4": {"bulletStyle": {"foregroundColor": {"opaqueColor": {"themeColor": "LIGHT1"}}, "fontSize": {"magnitude": 18, "unit": "PT"}}}, "5": {"bulletStyle": {"foregroundColor": {"opaqueColor": {"themeColor": "LIGHT1"}}, "fontSize": {"magnitude": 18, "unit": "PT"}}}, "6": {"bulletStyle": {"foregroundColor": {"opaqueColor": {"themeColor": "LIGHT1"}}, "fontSize": {"magnitude": 18, "unit": "PT"}}}, "7": {"bulletStyle": {"foregroundColor": {"opaqueColor": {"themeColor": "LIGHT1"}}, "fontSize": {"magnitude": 18, "unit": "PT"}}}, "8": {"bulletStyle": {"foregroundColor": {"opaqueColor": {"themeColor": "LIGHT1"}}, "fontSize": {"magnitude": 18, "unit": "PT"}}}}}}}, "shapeProperties": {"shapeBackgroundFill": {"propertyState": "INHERIT"}, "outline": {"propertyState": "INHERIT"}, "shadow": {"propertyState": "INHERIT"}, "autofit": {"fontScale": 1}}, "placeholder": {"type": "SUBTITLE", "parentObjectId": "gc6f919934_0_65"}}}, {"objectId": "gc6f919934_0_72", "size": {"width": {"magnitude": 3000000, "unit": "EMU"}, "height": {"magnitude": 3000000, "unit": "EMU"}}, "transform": {"scaleX": 0.18289999663829803, "scaleY": 0.13120000064373016, "translateX": 8523541.40625, "translateY": 4695623.046875, "unit": "EMU"}, "shape": {"shapeType": "TEXT_BOX", "text": {"textElements": [{"endIndex": 2, "paragraphMarker": {"style": {"direction": "LEFT_TO_RIGHT"}}}, {"endIndex": 1, "autoText": {"type": "SLIDE_NUMBER", "content": "#", "style": {}}}, {"startIndex": 1, "endIndex": 2, "textRun": {"content": "\n", "style": {}}}]}, "shapeProperties": {"shapeBackgroundFill": {"propertyState": "INHERIT"}, "outline": {"propertyState": "INHERIT"}, "shadow": {"propertyState": "INHERIT"}, "autofit": {"fontScale": 1}}, "placeholder": {"type": "SLIDE_NUMBER", "parentObjectId": "gc6f919934_0_66"}}}], "layoutProperties": {"masterObjectId": "gc6f919934_0_63", "name": "TITLE", "displayName": "<PERSON>rang trình bày chứa tiêu đề"}, "pageProperties": {"pageBackgroundFill": {"propertyState": "INHERIT"}}}], "masters": [{"objectId": "gc6f919934_0_63", "pageType": "MASTER", "pageElements": [{"objectId": "gc6f919934_0_64", "size": {"width": {"magnitude": 3000000, "unit": "EMU"}, "height": {"magnitude": 3000000, "unit": "EMU"}}, "transform": {"scaleX": 2.7407000064849854, "scaleY": 0.25589999556541443, "translateX": 471900, "translateY": 738725, "unit": "EMU"}, "shape": {"shapeType": "TEXT_BOX", "text": {"textElements": [{"endIndex": 1, "paragraphMarker": {"style": {"lineSpacing": 100, "alignment": "START", "indentStart": {"unit": "PT"}, "indentEnd": {"unit": "PT"}, "spaceAbove": {"unit": "PT"}, "spaceBelow": {"unit": "PT"}, "indentFirstLine": {"unit": "PT"}, "direction": "LEFT_TO_RIGHT", "spacingMode": "NEVER_COLLAPSE"}, "bullet": {"listId": "bodyPlaceholderListEntity", "glyph": " ", "bulletStyle": {}}}}, {"endIndex": 1, "textRun": {"content": "\n", "style": {"backgroundColor": {}, "foregroundColor": {"opaqueColor": {"themeColor": "LIGHT1"}}, "bold": false, "italic": false, "fontFamily": "Roboto", "fontSize": {"magnitude": 32, "unit": "PT"}, "baselineOffset": "NONE", "smallCaps": false, "strikethrough": false, "underline": false, "weightedFontFamily": {"fontFamily": "Roboto", "weight": 400}}}}, {"startIndex": 1, "endIndex": 2, "paragraphMarker": {"style": {"lineSpacing": 100, "alignment": "START", "indentStart": {"unit": "PT"}, "indentEnd": {"unit": "PT"}, "spaceAbove": {"unit": "PT"}, "spaceBelow": {"unit": "PT"}, "indentFirstLine": {"unit": "PT"}, "direction": "LEFT_TO_RIGHT", "spacingMode": "NEVER_COLLAPSE"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 1, "glyph": " ", "bulletStyle": {}}}}, {"startIndex": 1, "endIndex": 2, "textRun": {"content": "\n", "style": {"backgroundColor": {}, "foregroundColor": {"opaqueColor": {"themeColor": "LIGHT1"}}, "bold": false, "italic": false, "fontFamily": "Roboto", "fontSize": {"magnitude": 32, "unit": "PT"}, "baselineOffset": "NONE", "smallCaps": false, "strikethrough": false, "underline": false, "weightedFontFamily": {"fontFamily": "Roboto", "weight": 400}}}}, {"startIndex": 2, "endIndex": 3, "paragraphMarker": {"style": {"lineSpacing": 100, "alignment": "START", "indentStart": {"unit": "PT"}, "indentEnd": {"unit": "PT"}, "spaceAbove": {"unit": "PT"}, "spaceBelow": {"unit": "PT"}, "indentFirstLine": {"unit": "PT"}, "direction": "LEFT_TO_RIGHT", "spacingMode": "NEVER_COLLAPSE"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 2, "glyph": " ", "bulletStyle": {}}}}, {"startIndex": 2, "endIndex": 3, "textRun": {"content": "\n", "style": {"backgroundColor": {}, "foregroundColor": {"opaqueColor": {"themeColor": "LIGHT1"}}, "bold": false, "italic": false, "fontFamily": "Roboto", "fontSize": {"magnitude": 32, "unit": "PT"}, "baselineOffset": "NONE", "smallCaps": false, "strikethrough": false, "underline": false, "weightedFontFamily": {"fontFamily": "Roboto", "weight": 400}}}}, {"startIndex": 3, "endIndex": 4, "paragraphMarker": {"style": {"lineSpacing": 100, "alignment": "START", "indentStart": {"unit": "PT"}, "indentEnd": {"unit": "PT"}, "spaceAbove": {"unit": "PT"}, "spaceBelow": {"unit": "PT"}, "indentFirstLine": {"unit": "PT"}, "direction": "LEFT_TO_RIGHT", "spacingMode": "NEVER_COLLAPSE"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 3, "glyph": " ", "bulletStyle": {}}}}, {"startIndex": 3, "endIndex": 4, "textRun": {"content": "\n", "style": {"backgroundColor": {}, "foregroundColor": {"opaqueColor": {"themeColor": "LIGHT1"}}, "bold": false, "italic": false, "fontFamily": "Roboto", "fontSize": {"magnitude": 32, "unit": "PT"}, "baselineOffset": "NONE", "smallCaps": false, "strikethrough": false, "underline": false, "weightedFontFamily": {"fontFamily": "Roboto", "weight": 400}}}}, {"startIndex": 4, "endIndex": 5, "paragraphMarker": {"style": {"lineSpacing": 100, "alignment": "START", "indentStart": {"unit": "PT"}, "indentEnd": {"unit": "PT"}, "spaceAbove": {"unit": "PT"}, "spaceBelow": {"unit": "PT"}, "indentFirstLine": {"unit": "PT"}, "direction": "LEFT_TO_RIGHT", "spacingMode": "NEVER_COLLAPSE"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 4, "glyph": " ", "bulletStyle": {}}}}, {"startIndex": 4, "endIndex": 5, "textRun": {"content": "\n", "style": {"backgroundColor": {}, "foregroundColor": {"opaqueColor": {"themeColor": "LIGHT1"}}, "bold": false, "italic": false, "fontFamily": "Roboto", "fontSize": {"magnitude": 32, "unit": "PT"}, "baselineOffset": "NONE", "smallCaps": false, "strikethrough": false, "underline": false, "weightedFontFamily": {"fontFamily": "Roboto", "weight": 400}}}}, {"startIndex": 5, "endIndex": 6, "paragraphMarker": {"style": {"lineSpacing": 100, "alignment": "START", "indentStart": {"unit": "PT"}, "indentEnd": {"unit": "PT"}, "spaceAbove": {"unit": "PT"}, "spaceBelow": {"unit": "PT"}, "indentFirstLine": {"unit": "PT"}, "direction": "LEFT_TO_RIGHT", "spacingMode": "NEVER_COLLAPSE"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 5, "glyph": " ", "bulletStyle": {}}}}, {"startIndex": 5, "endIndex": 6, "textRun": {"content": "\n", "style": {"backgroundColor": {}, "foregroundColor": {"opaqueColor": {"themeColor": "LIGHT1"}}, "bold": false, "italic": false, "fontFamily": "Roboto", "fontSize": {"magnitude": 32, "unit": "PT"}, "baselineOffset": "NONE", "smallCaps": false, "strikethrough": false, "underline": false, "weightedFontFamily": {"fontFamily": "Roboto", "weight": 400}}}}, {"startIndex": 6, "endIndex": 7, "paragraphMarker": {"style": {"lineSpacing": 100, "alignment": "START", "indentStart": {"unit": "PT"}, "indentEnd": {"unit": "PT"}, "spaceAbove": {"unit": "PT"}, "spaceBelow": {"unit": "PT"}, "indentFirstLine": {"unit": "PT"}, "direction": "LEFT_TO_RIGHT", "spacingMode": "NEVER_COLLAPSE"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 6, "glyph": " ", "bulletStyle": {}}}}, {"startIndex": 6, "endIndex": 7, "textRun": {"content": "\n", "style": {"backgroundColor": {}, "foregroundColor": {"opaqueColor": {"themeColor": "LIGHT1"}}, "bold": false, "italic": false, "fontFamily": "Roboto", "fontSize": {"magnitude": 32, "unit": "PT"}, "baselineOffset": "NONE", "smallCaps": false, "strikethrough": false, "underline": false, "weightedFontFamily": {"fontFamily": "Roboto", "weight": 400}}}}, {"startIndex": 7, "endIndex": 8, "paragraphMarker": {"style": {"lineSpacing": 100, "alignment": "START", "indentStart": {"unit": "PT"}, "indentEnd": {"unit": "PT"}, "spaceAbove": {"unit": "PT"}, "spaceBelow": {"unit": "PT"}, "indentFirstLine": {"unit": "PT"}, "direction": "LEFT_TO_RIGHT", "spacingMode": "NEVER_COLLAPSE"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 7, "glyph": " ", "bulletStyle": {}}}}, {"startIndex": 7, "endIndex": 8, "textRun": {"content": "\n", "style": {"backgroundColor": {}, "foregroundColor": {"opaqueColor": {"themeColor": "LIGHT1"}}, "bold": false, "italic": false, "fontFamily": "Roboto", "fontSize": {"magnitude": 32, "unit": "PT"}, "baselineOffset": "NONE", "smallCaps": false, "strikethrough": false, "underline": false, "weightedFontFamily": {"fontFamily": "Roboto", "weight": 400}}}}, {"startIndex": 8, "endIndex": 9, "paragraphMarker": {"style": {"lineSpacing": 100, "alignment": "START", "indentStart": {"unit": "PT"}, "indentEnd": {"unit": "PT"}, "spaceAbove": {"unit": "PT"}, "spaceBelow": {"unit": "PT"}, "indentFirstLine": {"unit": "PT"}, "direction": "LEFT_TO_RIGHT", "spacingMode": "NEVER_COLLAPSE"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 8, "glyph": " ", "bulletStyle": {}}}}, {"startIndex": 8, "endIndex": 9, "textRun": {"content": "\n", "style": {"backgroundColor": {}, "foregroundColor": {"opaqueColor": {"themeColor": "LIGHT1"}}, "bold": false, "italic": false, "fontFamily": "Roboto", "fontSize": {"magnitude": 32, "unit": "PT"}, "baselineOffset": "NONE", "smallCaps": false, "strikethrough": false, "underline": false, "weightedFontFamily": {"fontFamily": "Roboto", "weight": 400}}}}], "lists": {"bodyPlaceholderListEntity": {"listId": "bodyPlaceholderListEntity", "nestingLevel": {"0": {"bulletStyle": {"backgroundColor": {}, "foregroundColor": {"opaqueColor": {"themeColor": "LIGHT1"}}, "bold": false, "italic": false, "fontFamily": "Roboto", "fontSize": {"magnitude": 32, "unit": "PT"}, "baselineOffset": "NONE", "smallCaps": false, "strikethrough": false, "underline": false, "weightedFontFamily": {"fontFamily": "Roboto", "weight": 400}}}, "1": {"bulletStyle": {"backgroundColor": {}, "foregroundColor": {"opaqueColor": {"themeColor": "LIGHT1"}}, "bold": false, "italic": false, "fontFamily": "Roboto", "fontSize": {"magnitude": 32, "unit": "PT"}, "baselineOffset": "NONE", "smallCaps": false, "strikethrough": false, "underline": false, "weightedFontFamily": {"fontFamily": "Roboto", "weight": 400}}}, "2": {"bulletStyle": {"backgroundColor": {}, "foregroundColor": {"opaqueColor": {"themeColor": "LIGHT1"}}, "bold": false, "italic": false, "fontFamily": "Roboto", "fontSize": {"magnitude": 32, "unit": "PT"}, "baselineOffset": "NONE", "smallCaps": false, "strikethrough": false, "underline": false, "weightedFontFamily": {"fontFamily": "Roboto", "weight": 400}}}, "3": {"bulletStyle": {"backgroundColor": {}, "foregroundColor": {"opaqueColor": {"themeColor": "LIGHT1"}}, "bold": false, "italic": false, "fontFamily": "Roboto", "fontSize": {"magnitude": 32, "unit": "PT"}, "baselineOffset": "NONE", "smallCaps": false, "strikethrough": false, "underline": false, "weightedFontFamily": {"fontFamily": "Roboto", "weight": 400}}}, "4": {"bulletStyle": {"backgroundColor": {}, "foregroundColor": {"opaqueColor": {"themeColor": "LIGHT1"}}, "bold": false, "italic": false, "fontFamily": "Roboto", "fontSize": {"magnitude": 32, "unit": "PT"}, "baselineOffset": "NONE", "smallCaps": false, "strikethrough": false, "underline": false, "weightedFontFamily": {"fontFamily": "Roboto", "weight": 400}}}, "5": {"bulletStyle": {"backgroundColor": {}, "foregroundColor": {"opaqueColor": {"themeColor": "LIGHT1"}}, "bold": false, "italic": false, "fontFamily": "Roboto", "fontSize": {"magnitude": 32, "unit": "PT"}, "baselineOffset": "NONE", "smallCaps": false, "strikethrough": false, "underline": false, "weightedFontFamily": {"fontFamily": "Roboto", "weight": 400}}}, "6": {"bulletStyle": {"backgroundColor": {}, "foregroundColor": {"opaqueColor": {"themeColor": "LIGHT1"}}, "bold": false, "italic": false, "fontFamily": "Roboto", "fontSize": {"magnitude": 32, "unit": "PT"}, "baselineOffset": "NONE", "smallCaps": false, "strikethrough": false, "underline": false, "weightedFontFamily": {"fontFamily": "Roboto", "weight": 400}}}, "7": {"bulletStyle": {"backgroundColor": {}, "foregroundColor": {"opaqueColor": {"themeColor": "LIGHT1"}}, "bold": false, "italic": false, "fontFamily": "Roboto", "fontSize": {"magnitude": 32, "unit": "PT"}, "baselineOffset": "NONE", "smallCaps": false, "strikethrough": false, "underline": false, "weightedFontFamily": {"fontFamily": "Roboto", "weight": 400}}}, "8": {"bulletStyle": {"backgroundColor": {}, "foregroundColor": {"opaqueColor": {"themeColor": "LIGHT1"}}, "bold": false, "italic": false, "fontFamily": "Roboto", "fontSize": {"magnitude": 32, "unit": "PT"}, "baselineOffset": "NONE", "smallCaps": false, "strikethrough": false, "underline": false, "weightedFontFamily": {"fontFamily": "Roboto", "weight": 400}}}}}}}, "shapeProperties": {"shapeBackgroundFill": {"propertyState": "NOT_RENDERED", "solidFill": {"color": {"rgbColor": {"red": 1, "green": 1, "blue": 1}}, "alpha": 1}}, "outline": {"outlineFill": {"solidFill": {"color": {"rgbColor": {}}, "alpha": 1}}, "weight": {"magnitude": 9525, "unit": "EMU"}, "dashStyle": "SOLID", "propertyState": "NOT_RENDERED"}, "shadow": {"type": "OUTER", "transform": {"scaleX": 1, "scaleY": 1, "unit": "EMU"}, "alignment": "BOTTOM_LEFT", "blurRadius": {"unit": "EMU"}, "color": {"rgbColor": {}}, "alpha": 1, "rotateWithShape": false, "propertyState": "NOT_RENDERED"}, "contentAlignment": "BOTTOM", "autofit": {"autofitType": "NONE", "fontScale": 1}}, "placeholder": {"type": "TITLE"}}}, {"objectId": "gc6f919934_0_65", "size": {"width": {"magnitude": 3000000, "unit": "EMU"}, "height": {"magnitude": 3000000, "unit": "EMU"}}, "transform": {"scaleX": 2.7407000064849854, "scaleY": 0.9034000039100647, "translateX": 471900, "translateY": 1919075, "unit": "EMU"}, "shape": {"shapeType": "TEXT_BOX", "text": {"textElements": [{"endIndex": 1, "paragraphMarker": {"style": {"lineSpacing": 115, "alignment": "START", "indentStart": {"unit": "PT"}, "indentEnd": {"unit": "PT"}, "spaceAbove": {"unit": "PT"}, "spaceBelow": {"magnitude": 16, "unit": "PT"}, "indentFirstLine": {"unit": "PT"}, "direction": "LEFT_TO_RIGHT", "spacingMode": "NEVER_COLLAPSE"}, "bullet": {"listId": "bodyPlaceholderListEntity", "glyph": "●", "bulletStyle": {}}}}, {"endIndex": 1, "textRun": {"content": "\n", "style": {"backgroundColor": {}, "foregroundColor": {"opaqueColor": {"themeColor": "LIGHT2"}}, "bold": false, "italic": false, "fontFamily": "Roboto", "fontSize": {"magnitude": 18, "unit": "PT"}, "baselineOffset": "NONE", "smallCaps": false, "strikethrough": false, "underline": false, "weightedFontFamily": {"fontFamily": "Roboto", "weight": 400}}}}, {"startIndex": 1, "endIndex": 2, "paragraphMarker": {"style": {"lineSpacing": 115, "alignment": "START", "indentStart": {"unit": "PT"}, "indentEnd": {"unit": "PT"}, "spaceAbove": {"unit": "PT"}, "spaceBelow": {"magnitude": 16, "unit": "PT"}, "indentFirstLine": {"unit": "PT"}, "direction": "LEFT_TO_RIGHT", "spacingMode": "NEVER_COLLAPSE"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 1, "glyph": "○", "bulletStyle": {}}}}, {"startIndex": 1, "endIndex": 2, "textRun": {"content": "\n", "style": {"backgroundColor": {}, "foregroundColor": {"opaqueColor": {"themeColor": "LIGHT2"}}, "bold": false, "italic": false, "fontFamily": "Roboto", "fontSize": {"magnitude": 14, "unit": "PT"}, "baselineOffset": "NONE", "smallCaps": false, "strikethrough": false, "underline": false, "weightedFontFamily": {"fontFamily": "Roboto", "weight": 400}}}}, {"startIndex": 2, "endIndex": 3, "paragraphMarker": {"style": {"lineSpacing": 115, "alignment": "START", "indentStart": {"unit": "PT"}, "indentEnd": {"unit": "PT"}, "spaceAbove": {"unit": "PT"}, "spaceBelow": {"magnitude": 16, "unit": "PT"}, "indentFirstLine": {"unit": "PT"}, "direction": "LEFT_TO_RIGHT", "spacingMode": "NEVER_COLLAPSE"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 2, "glyph": "■", "bulletStyle": {}}}}, {"startIndex": 2, "endIndex": 3, "textRun": {"content": "\n", "style": {"backgroundColor": {}, "foregroundColor": {"opaqueColor": {"themeColor": "LIGHT2"}}, "bold": false, "italic": false, "fontFamily": "Roboto", "fontSize": {"magnitude": 14, "unit": "PT"}, "baselineOffset": "NONE", "smallCaps": false, "strikethrough": false, "underline": false, "weightedFontFamily": {"fontFamily": "Roboto", "weight": 400}}}}, {"startIndex": 3, "endIndex": 4, "paragraphMarker": {"style": {"lineSpacing": 115, "alignment": "START", "indentStart": {"unit": "PT"}, "indentEnd": {"unit": "PT"}, "spaceAbove": {"unit": "PT"}, "spaceBelow": {"magnitude": 16, "unit": "PT"}, "indentFirstLine": {"unit": "PT"}, "direction": "LEFT_TO_RIGHT", "spacingMode": "NEVER_COLLAPSE"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 3, "glyph": "●", "bulletStyle": {}}}}, {"startIndex": 3, "endIndex": 4, "textRun": {"content": "\n", "style": {"backgroundColor": {}, "foregroundColor": {"opaqueColor": {"themeColor": "LIGHT2"}}, "bold": false, "italic": false, "fontFamily": "Roboto", "fontSize": {"magnitude": 14, "unit": "PT"}, "baselineOffset": "NONE", "smallCaps": false, "strikethrough": false, "underline": false, "weightedFontFamily": {"fontFamily": "Roboto", "weight": 400}}}}, {"startIndex": 4, "endIndex": 5, "paragraphMarker": {"style": {"lineSpacing": 115, "alignment": "START", "indentStart": {"unit": "PT"}, "indentEnd": {"unit": "PT"}, "spaceAbove": {"unit": "PT"}, "spaceBelow": {"magnitude": 16, "unit": "PT"}, "indentFirstLine": {"unit": "PT"}, "direction": "LEFT_TO_RIGHT", "spacingMode": "NEVER_COLLAPSE"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 4, "glyph": "○", "bulletStyle": {}}}}, {"startIndex": 4, "endIndex": 5, "textRun": {"content": "\n", "style": {"backgroundColor": {}, "foregroundColor": {"opaqueColor": {"themeColor": "LIGHT2"}}, "bold": false, "italic": false, "fontFamily": "Roboto", "fontSize": {"magnitude": 14, "unit": "PT"}, "baselineOffset": "NONE", "smallCaps": false, "strikethrough": false, "underline": false, "weightedFontFamily": {"fontFamily": "Roboto", "weight": 400}}}}, {"startIndex": 5, "endIndex": 6, "paragraphMarker": {"style": {"lineSpacing": 115, "alignment": "START", "indentStart": {"unit": "PT"}, "indentEnd": {"unit": "PT"}, "spaceAbove": {"unit": "PT"}, "spaceBelow": {"magnitude": 16, "unit": "PT"}, "indentFirstLine": {"unit": "PT"}, "direction": "LEFT_TO_RIGHT", "spacingMode": "NEVER_COLLAPSE"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 5, "glyph": "■", "bulletStyle": {}}}}, {"startIndex": 5, "endIndex": 6, "textRun": {"content": "\n", "style": {"backgroundColor": {}, "foregroundColor": {"opaqueColor": {"themeColor": "LIGHT2"}}, "bold": false, "italic": false, "fontFamily": "Roboto", "fontSize": {"magnitude": 14, "unit": "PT"}, "baselineOffset": "NONE", "smallCaps": false, "strikethrough": false, "underline": false, "weightedFontFamily": {"fontFamily": "Roboto", "weight": 400}}}}, {"startIndex": 6, "endIndex": 7, "paragraphMarker": {"style": {"lineSpacing": 115, "alignment": "START", "indentStart": {"unit": "PT"}, "indentEnd": {"unit": "PT"}, "spaceAbove": {"unit": "PT"}, "spaceBelow": {"magnitude": 16, "unit": "PT"}, "indentFirstLine": {"unit": "PT"}, "direction": "LEFT_TO_RIGHT", "spacingMode": "NEVER_COLLAPSE"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 6, "glyph": "●", "bulletStyle": {}}}}, {"startIndex": 6, "endIndex": 7, "textRun": {"content": "\n", "style": {"backgroundColor": {}, "foregroundColor": {"opaqueColor": {"themeColor": "LIGHT2"}}, "bold": false, "italic": false, "fontFamily": "Roboto", "fontSize": {"magnitude": 14, "unit": "PT"}, "baselineOffset": "NONE", "smallCaps": false, "strikethrough": false, "underline": false, "weightedFontFamily": {"fontFamily": "Roboto", "weight": 400}}}}, {"startIndex": 7, "endIndex": 8, "paragraphMarker": {"style": {"lineSpacing": 115, "alignment": "START", "indentStart": {"unit": "PT"}, "indentEnd": {"unit": "PT"}, "spaceAbove": {"unit": "PT"}, "spaceBelow": {"magnitude": 16, "unit": "PT"}, "indentFirstLine": {"unit": "PT"}, "direction": "LEFT_TO_RIGHT", "spacingMode": "NEVER_COLLAPSE"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 7, "glyph": "○", "bulletStyle": {}}}}, {"startIndex": 7, "endIndex": 8, "textRun": {"content": "\n", "style": {"backgroundColor": {}, "foregroundColor": {"opaqueColor": {"themeColor": "LIGHT2"}}, "bold": false, "italic": false, "fontFamily": "Roboto", "fontSize": {"magnitude": 14, "unit": "PT"}, "baselineOffset": "NONE", "smallCaps": false, "strikethrough": false, "underline": false, "weightedFontFamily": {"fontFamily": "Roboto", "weight": 400}}}}, {"startIndex": 8, "endIndex": 9, "paragraphMarker": {"style": {"lineSpacing": 115, "alignment": "START", "indentStart": {"unit": "PT"}, "indentEnd": {"unit": "PT"}, "spaceAbove": {"unit": "PT"}, "spaceBelow": {"magnitude": 16, "unit": "PT"}, "indentFirstLine": {"unit": "PT"}, "direction": "LEFT_TO_RIGHT", "spacingMode": "NEVER_COLLAPSE"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 8, "glyph": "■", "bulletStyle": {}}}}, {"startIndex": 8, "endIndex": 9, "textRun": {"content": "\n", "style": {"backgroundColor": {}, "foregroundColor": {"opaqueColor": {"themeColor": "LIGHT2"}}, "bold": false, "italic": false, "fontFamily": "Roboto", "fontSize": {"magnitude": 14, "unit": "PT"}, "baselineOffset": "NONE", "smallCaps": false, "strikethrough": false, "underline": false, "weightedFontFamily": {"fontFamily": "Roboto", "weight": 400}}}}], "lists": {"bodyPlaceholderListEntity": {"listId": "bodyPlaceholderListEntity", "nestingLevel": {"0": {"bulletStyle": {"backgroundColor": {}, "foregroundColor": {"opaqueColor": {"themeColor": "LIGHT2"}}, "bold": false, "italic": false, "fontFamily": "Roboto", "fontSize": {"magnitude": 18, "unit": "PT"}, "baselineOffset": "NONE", "smallCaps": false, "strikethrough": false, "underline": false, "weightedFontFamily": {"fontFamily": "Roboto", "weight": 400}}}, "1": {"bulletStyle": {"backgroundColor": {}, "foregroundColor": {"opaqueColor": {"themeColor": "LIGHT2"}}, "bold": false, "italic": false, "fontFamily": "Roboto", "fontSize": {"magnitude": 14, "unit": "PT"}, "baselineOffset": "NONE", "smallCaps": false, "strikethrough": false, "underline": false, "weightedFontFamily": {"fontFamily": "Roboto", "weight": 400}}}, "2": {"bulletStyle": {"backgroundColor": {}, "foregroundColor": {"opaqueColor": {"themeColor": "LIGHT2"}}, "bold": false, "italic": false, "fontFamily": "Roboto", "fontSize": {"magnitude": 14, "unit": "PT"}, "baselineOffset": "NONE", "smallCaps": false, "strikethrough": false, "underline": false, "weightedFontFamily": {"fontFamily": "Roboto", "weight": 400}}}, "3": {"bulletStyle": {"backgroundColor": {}, "foregroundColor": {"opaqueColor": {"themeColor": "LIGHT2"}}, "bold": false, "italic": false, "fontFamily": "Roboto", "fontSize": {"magnitude": 14, "unit": "PT"}, "baselineOffset": "NONE", "smallCaps": false, "strikethrough": false, "underline": false, "weightedFontFamily": {"fontFamily": "Roboto", "weight": 400}}}, "4": {"bulletStyle": {"backgroundColor": {}, "foregroundColor": {"opaqueColor": {"themeColor": "LIGHT2"}}, "bold": false, "italic": false, "fontFamily": "Roboto", "fontSize": {"magnitude": 14, "unit": "PT"}, "baselineOffset": "NONE", "smallCaps": false, "strikethrough": false, "underline": false, "weightedFontFamily": {"fontFamily": "Roboto", "weight": 400}}}, "5": {"bulletStyle": {"backgroundColor": {}, "foregroundColor": {"opaqueColor": {"themeColor": "LIGHT2"}}, "bold": false, "italic": false, "fontFamily": "Roboto", "fontSize": {"magnitude": 14, "unit": "PT"}, "baselineOffset": "NONE", "smallCaps": false, "strikethrough": false, "underline": false, "weightedFontFamily": {"fontFamily": "Roboto", "weight": 400}}}, "6": {"bulletStyle": {"backgroundColor": {}, "foregroundColor": {"opaqueColor": {"themeColor": "LIGHT2"}}, "bold": false, "italic": false, "fontFamily": "Roboto", "fontSize": {"magnitude": 14, "unit": "PT"}, "baselineOffset": "NONE", "smallCaps": false, "strikethrough": false, "underline": false, "weightedFontFamily": {"fontFamily": "Roboto", "weight": 400}}}, "7": {"bulletStyle": {"backgroundColor": {}, "foregroundColor": {"opaqueColor": {"themeColor": "LIGHT2"}}, "bold": false, "italic": false, "fontFamily": "Roboto", "fontSize": {"magnitude": 14, "unit": "PT"}, "baselineOffset": "NONE", "smallCaps": false, "strikethrough": false, "underline": false, "weightedFontFamily": {"fontFamily": "Roboto", "weight": 400}}}, "8": {"bulletStyle": {"backgroundColor": {}, "foregroundColor": {"opaqueColor": {"themeColor": "LIGHT2"}}, "bold": false, "italic": false, "fontFamily": "Roboto", "fontSize": {"magnitude": 14, "unit": "PT"}, "baselineOffset": "NONE", "smallCaps": false, "strikethrough": false, "underline": false, "weightedFontFamily": {"fontFamily": "Roboto", "weight": 400}}}}}}}, "shapeProperties": {"shapeBackgroundFill": {"propertyState": "NOT_RENDERED", "solidFill": {"color": {"rgbColor": {"red": 1, "green": 1, "blue": 1}}, "alpha": 1}}, "outline": {"outlineFill": {"solidFill": {"color": {"rgbColor": {}}, "alpha": 1}}, "weight": {"magnitude": 9525, "unit": "EMU"}, "dashStyle": "SOLID", "propertyState": "NOT_RENDERED"}, "shadow": {"type": "OUTER", "transform": {"scaleX": 1, "scaleY": 1, "unit": "EMU"}, "alignment": "BOTTOM_LEFT", "blurRadius": {"unit": "EMU"}, "color": {"rgbColor": {}}, "alpha": 1, "rotateWithShape": false, "propertyState": "NOT_RENDERED"}, "contentAlignment": "TOP", "autofit": {"autofitType": "NONE", "fontScale": 1}}, "placeholder": {"type": "BODY"}}}, {"objectId": "gc6f919934_0_66", "size": {"width": {"magnitude": 3000000, "unit": "EMU"}, "height": {"magnitude": 3000000, "unit": "EMU"}}, "transform": {"scaleX": 0.18289999663829803, "scaleY": 0.13120000064373016, "translateX": 8523541.40625, "translateY": 4695623.046875, "unit": "EMU"}, "shape": {"shapeType": "TEXT_BOX", "text": {"textElements": [{"endIndex": 2, "paragraphMarker": {"style": {"lineSpacing": 100, "alignment": "END", "indentStart": {"unit": "PT"}, "indentEnd": {"unit": "PT"}, "spaceAbove": {"unit": "PT"}, "spaceBelow": {"unit": "PT"}, "indentFirstLine": {"unit": "PT"}, "direction": "LEFT_TO_RIGHT", "spacingMode": "NEVER_COLLAPSE"}}}, {"endIndex": 1, "autoText": {"type": "SLIDE_NUMBER", "content": "#", "style": {"backgroundColor": {}, "foregroundColor": {"opaqueColor": {"themeColor": "LIGHT2"}}, "bold": false, "italic": false, "fontFamily": "Roboto", "fontSize": {"magnitude": 10, "unit": "PT"}, "baselineOffset": "NONE", "smallCaps": false, "strikethrough": false, "underline": false, "weightedFontFamily": {"fontFamily": "Roboto", "weight": 400}}}}, {"startIndex": 1, "endIndex": 2, "textRun": {"content": "\n", "style": {"backgroundColor": {}, "foregroundColor": {"opaqueColor": {"themeColor": "LIGHT2"}}, "bold": false, "italic": false, "fontFamily": "Roboto", "fontSize": {"magnitude": 10, "unit": "PT"}, "baselineOffset": "NONE", "smallCaps": false, "strikethrough": false, "underline": false, "weightedFontFamily": {"fontFamily": "Roboto", "weight": 400}}}}]}, "shapeProperties": {"shapeBackgroundFill": {"propertyState": "NOT_RENDERED", "solidFill": {"color": {"rgbColor": {"red": 1, "green": 1, "blue": 1}}, "alpha": 1}}, "outline": {"outlineFill": {"solidFill": {"color": {"rgbColor": {}}, "alpha": 1}}, "weight": {"magnitude": 9525, "unit": "EMU"}, "dashStyle": "SOLID", "propertyState": "NOT_RENDERED"}, "shadow": {"type": "OUTER", "transform": {"scaleX": 1, "scaleY": 1, "unit": "EMU"}, "alignment": "BOTTOM_LEFT", "blurRadius": {"unit": "EMU"}, "color": {"rgbColor": {}}, "alpha": 1, "rotateWithShape": false, "propertyState": "NOT_RENDERED"}, "contentAlignment": "MIDDLE", "autofit": {"autofitType": "NONE", "fontScale": 1}}, "placeholder": {"type": "SLIDE_NUMBER"}}}], "pageProperties": {"pageBackgroundFill": {"solidFill": {"color": {"themeColor": "DARK1"}, "alpha": 1}}, "colorScheme": {"colors": [{"type": "DARK1", "color": {"red": 0.25882354, "green": 0.52156866, "blue": 0.95686275}}, {"type": "LIGHT1", "color": {"red": 1, "green": 1, "blue": 1}}, {"type": "DARK2", "color": {"red": 0.25882354, "green": 0.25882354, "blue": 0.25882354}}, {"type": "LIGHT2", "color": {"red": 0.4509804, "green": 0.4509804, "blue": 0.4509804}}, {"type": "ACCENT1", "color": {"red": 0.007843138, "green": 0.46666667, "blue": 0.7411765}}, {"type": "ACCENT2", "color": {"red": 0.05882353, "green": 0.6156863, "blue": 0.34509805}}, {"type": "ACCENT3", "color": {"red": 0.85882354, "green": 0.26666668, "blue": 0.21568628}}, {"type": "ACCENT4", "color": {"red": 0.98039216, "green": 0.98039216, "blue": 0.98039216}}, {"type": "ACCENT5", "color": {"red": 0.30980393, "green": 0.7647059, "blue": 0.96862745}}, {"type": "ACCENT6", "color": {"red": 0.95686275, "green": 0.7058824}}, {"type": "HYPERLINK", "color": {"red": 0.30980393, "green": 0.7647059, "blue": 0.96862745}}, {"type": "FOLLOWED_HYPERLINK", "color": {"red": 0.30980393, "green": 0.7647059, "blue": 0.96862745}}, {"type": "TEXT1", "color": {"red": 0.25882354, "green": 0.52156866, "blue": 0.95686275}}, {"type": "BACKGROUND1", "color": {"red": 1, "green": 1, "blue": 1}}, {"type": "TEXT2", "color": {"red": 0.4509804, "green": 0.4509804, "blue": 0.4509804}}, {"type": "BACKGROUND2", "color": {"red": 0.25882354, "green": 0.25882354, "blue": 0.25882354}}]}}, "masterProperties": {"displayName": "Material"}}]}