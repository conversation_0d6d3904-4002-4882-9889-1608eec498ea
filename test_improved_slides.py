"""
Test slide generation với cải tiến mới
"""

import requests
import json

def test_improved_slide_generation():
    """Test với config prompt cải tiến"""
    
    url = "http://localhost:8000/api/v1/slides/generate-slides"
    
    payload = {
        "lesson_id": "1",
        "template_id": "19QE39G1EFZGdm-_m3TY1poD5v1we2T5Z_rglalK_4mA",
        "config_prompt": """
        Tạo slide chuyên nghiệp cho bài học Hóa học:
        - Mỗi slide chỉ 1 chủ đề chính
        - Bullet points ngắn gọn, d<PERSON> hiểu
        - <PERSON>ạo thêm slides nếu cần để trình bày đầy đủ
        - B<PERSON> cục rõ ràng, không chen chúc
        - <PERSON><PERSON> hợp với học sinh THPT
        """,
        "presentation_title": "<PERSON><PERSON><PERSON> học <PERSON> học - Nguyên tử"
    }
    
    print("🧪 TESTING IMPROVED SLIDE GENERATION")
    print("=" * 50)
    print(f"📝 Payload: {json.dumps(payload, indent=2, ensure_ascii=False)}")
    print()
    
    try:
        response = requests.post(url, json=payload, timeout=120)
        
        print(f"📊 Status Code: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ SUCCESS!")
            print(f"🔗 Presentation Link: {result.get('web_view_link')}")
            print(f"📄 Presentation ID: {result.get('presentation_id')}")
            print(f"📊 Slides Created: {result.get('slides_created')}")
            print(f"📋 Title: {result.get('presentation_title')}")
            
            print("\n" + "=" * 50)
            print("📋 FULL RESPONSE:")
            print(json.dumps(result, indent=2, ensure_ascii=False))
            
            print("\n" + "=" * 50)
            print("💡 NEXT STEPS:")
            print("1. Mở link Google Slides để kiểm tra chất lượng")
            print("2. Kiểm tra xem nội dung có bị đè nhau không")
            print("3. Xem số lượng slides có phù hợp với nội dung không")
            print("4. Đánh giá tính thẩm mỹ và dễ đọc")
            
        else:
            print("❌ FAILED!")
            try:
                error = response.json()
                print(f"Error: {json.dumps(error, indent=2, ensure_ascii=False)}")
            except:
                print(f"Raw error: {response.text}")
                
    except Exception as e:
        print(f"❌ Exception: {e}")

def test_multiple_configs():
    """Test với nhiều config khác nhau"""
    
    configs = [
        {
            "name": "Cơ bản",
            "prompt": "Tạo slide đơn giản, dễ hiểu"
        },
        {
            "name": "Chi tiết",
            "prompt": """
            Tạo slide chi tiết với:
            - Nhiều thông tin khoa học
            - Ví dụ cụ thể
            - Hình ảnh minh họa (mô tả text)
            """
        },
        {
            "name": "Tương tác",
            "prompt": """
            Tạo slide tương tác với:
            - Câu hỏi cho học sinh
            - Hoạt động thực hành
            - Bài tập áp dụng
            """
        }
    ]
    
    print("\n🔄 TESTING MULTIPLE CONFIGURATIONS")
    print("=" * 50)
    
    for i, config in enumerate(configs, 1):
        print(f"\n📋 Test {i}: {config['name']}")
        print("-" * 30)
        
        payload = {
            "lesson_id": "1",
            "template_id": "19QE39G1EFZGdm-_m3TY1poD5v1we2T5Z_rglalK_4mA",
            "config_prompt": config['prompt'],
            "presentation_title": f"Test {config['name']} - Nguyên tử"
        }
        
        try:
            response = requests.post(
                "http://localhost:8000/api/v1/slides/generate-slides",
                json=payload,
                timeout=120
            )
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ {config['name']}: {result.get('slides_created')} slides")
                print(f"🔗 Link: {result.get('web_view_link')}")
            else:
                print(f"❌ {config['name']}: Failed ({response.status_code})")
                
        except Exception as e:
            print(f"❌ {config['name']}: Exception - {e}")

if __name__ == "__main__":
    # Test chính
    test_improved_slide_generation()
    
    # Test nhiều config (optional)
    # test_multiple_configs()
