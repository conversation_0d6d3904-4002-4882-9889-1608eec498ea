"""
Test script cho Slide Generation API
"""

import requests
import json
import time

# API base URL
BASE_URL = "http://localhost:8000/api/v1"

def test_health_check():
    """Test health check endpoint"""
    print("=== Testing Health Check ===")
    
    response = requests.get(f"{BASE_URL}/slides/health")
    print(f"Status Code: {response.status_code}")
    print(f"Response: {json.dumps(response.json(), indent=2)}")
    print()

def test_template_info(template_id):
    """Test template info endpoint"""
    print(f"=== Testing Template Info for {template_id} ===")
    
    response = requests.get(f"{BASE_URL}/slides/template-info/{template_id}")
    print(f"Status Code: {response.status_code}")
    
    if response.status_code == 200:
        print(f"Response: {json.dumps(response.json(), indent=2)}")
    else:
        print(f"Error: {response.text}")
    print()

def test_generate_slides_sync(lesson_id, template_id):
    """Test synchronous slide generation"""
    print(f"=== Testing Sync Slide Generation ===")
    print(f"Lesson ID: {lesson_id}")
    print(f"Template ID: {template_id}")
    
    payload = {
        "lesson_id": lesson_id,
        "template_id": template_id,
        "config_prompt": "Tạo slide với phong cách sinh động, phù hợp với học sinh trung học phổ thông"
    }
    
    response = requests.post(f"{BASE_URL}/slides/generate-slides", json=payload)
    print(f"Status Code: {response.status_code}")
    
    if response.status_code == 200:
        result = response.json()
        print(f"Success: {result.get('success')}")
        print(f"Presentation ID: {result.get('presentation_id')}")
        print(f"Web View Link: {result.get('web_view_link')}")
        print(f"Slides Created: {result.get('slides_created')}")
    else:
        print(f"Error: {response.text}")
    print()

def test_generate_slides_async(lesson_id, template_id):
    """Test asynchronous slide generation"""
    print(f"=== Testing Async Slide Generation ===")
    print(f"Lesson ID: {lesson_id}")
    print(f"Template ID: {template_id}")
    
    payload = {
        "lesson_id": lesson_id,
        "template_id": template_id,
        "config_prompt": "Tạo slide với phong cách sinh động, phù hợp với học sinh trung học phổ thông"
    }
    
    response = requests.post(f"{BASE_URL}/slides/generate-slides-async", json=payload)
    print(f"Status Code: {response.status_code}")
    
    if response.status_code == 200:
        result = response.json()
        task_id = result.get('task_id')
        print(f"Task ID: {task_id}")
        print(f"Status: {result.get('status')}")
        print(f"Message: {result.get('message')}")
        
        # Monitor task progress
        if task_id:
            monitor_task_progress(task_id)
    else:
        print(f"Error: {response.text}")
    print()

def monitor_task_progress(task_id):
    """Monitor task progress"""
    print(f"=== Monitoring Task Progress: {task_id} ===")
    
    max_attempts = 30  # 5 minutes with 10 second intervals
    attempt = 0
    
    while attempt < max_attempts:
        try:
            response = requests.get(f"{BASE_URL}/tasks/{task_id}/status")
            
            if response.status_code == 200:
                result = response.json()
                status = result.get('status')
                progress = result.get('progress', 0)
                message = result.get('message', '')
                
                print(f"[{attempt+1:2d}] Status: {status:10s} Progress: {progress:3d}% - {message}")
                
                if status in ['SUCCESS', 'FAILURE']:
                    if status == 'SUCCESS':
                        task_result = result.get('result', {})
                        if task_result.get('web_view_link'):
                            print(f"✅ Slide created successfully!")
                            print(f"🔗 Link: {task_result['web_view_link']}")
                    else:
                        print(f"❌ Task failed: {message}")
                    break
                    
            else:
                print(f"Error checking task status: {response.text}")
                
        except Exception as e:
            print(f"Error: {e}")
        
        attempt += 1
        time.sleep(10)  # Wait 10 seconds before next check
    
    if attempt >= max_attempts:
        print("⏰ Timeout waiting for task completion")

def main():
    """Main test function"""
    print("🚀 Starting Slide Generation API Tests")
    print("=" * 50)
    
    # Test health check first
    test_health_check()
    
    # Test với template ID mẫu (cần thay thế bằng template thực tế)
    # Để test, bạn cần:
    # 1. Tạo một Google Slides template
    # 2. Lấy ID từ URL (phần sau /d/ và trước /edit)
    # 3. Thay thế template_id dưới đây
    
    template_id = "1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms"  # Template ID mẫu
    lesson_id = "lesson_123"  # Lesson ID mẫu
    
    print(f"📝 Using Template ID: {template_id}")
    print(f"📚 Using Lesson ID: {lesson_id}")
    print()
    
    # Test template info
    test_template_info(template_id)
    
    # Test sync generation (comment out if you want to test async only)
    # test_generate_slides_sync(lesson_id, template_id)
    
    # Test async generation
    test_generate_slides_async(lesson_id, template_id)
    
    print("✅ Tests completed!")

if __name__ == "__main__":
    main()
