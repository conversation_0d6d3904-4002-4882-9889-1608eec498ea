"""
Debug script để kiểm tra cấu trúc thực tế của Google Slides API response
"""

import json
import asyncio
from app.services.google_slides_service import get_google_slides_service

async def debug_slides_api_response():
    """Debug actual API response structure"""
    template_id = "19QE39G1EFZGdm-_m3TY1poD5v1we2T5Z_rglalK_4mA"
    
    print(f"🔍 Debugging Google Slides API response for template: {template_id}")
    print("=" * 80)
    
    # Get slides service
    slides_service = get_google_slides_service()
    
    if not slides_service.is_available():
        print("❌ Google Slides service not available")
        return
    
    try:
        # Get raw presentation data
        presentation = slides_service.slides_service.presentations().get(
            presentationId=template_id
        ).execute()
        
        print("📊 PRESENTATION OVERVIEW:")
        print(f"  Title: {presentation.get('title')}")
        print(f"  Presentation ID: {presentation.get('presentationId')}")
        print(f"  Total Slides: {len(presentation.get('slides', []))}")
        print()
        
        # Debug first slide in detail
        slides = presentation.get('slides', [])
        if slides:
            first_slide = slides[0]
            print("🔍 FIRST SLIDE DETAILED STRUCTURE:")
            print(f"  Slide ID: {first_slide.get('objectId')}")
            print(f"  Page Elements Count: {len(first_slide.get('pageElements', []))}")
            print()
            
            # Debug each page element
            for i, element in enumerate(first_slide.get('pageElements', [])):
                print(f"  📋 ELEMENT {i+1}:")
                print(f"    Object ID: {element.get('objectId')}")
                print(f"    Element Keys: {list(element.keys())}")
                
                # Check if size exists
                if 'size' in element:
                    size = element['size']
                    print(f"    Size: {size}")
                    print(f"    Size Keys: {list(size.keys()) if isinstance(size, dict) else 'Not a dict'}")
                else:
                    print(f"    Size: NOT FOUND")
                
                # Check if transform exists
                if 'transform' in element:
                    transform = element['transform']
                    print(f"    Transform: {transform}")
                    print(f"    Transform Keys: {list(transform.keys()) if isinstance(transform, dict) else 'Not a dict'}")
                else:
                    print(f"    Transform: NOT FOUND")
                
                # Check shape content if exists
                if 'shape' in element:
                    shape = element['shape']
                    print(f"    Shape Keys: {list(shape.keys())}")
                    
                    # Check text content
                    if 'text' in shape:
                        text_content = shape['text']
                        print(f"    Text Content Keys: {list(text_content.keys())}")
                        
                        # Extract actual text
                        if 'textElements' in text_content:
                            text_parts = []
                            for te in text_content['textElements']:
                                if 'textRun' in te:
                                    text_parts.append(te['textRun'].get('content', ''))
                            actual_text = ''.join(text_parts).strip()
                            print(f"    Actual Text: '{actual_text}'")
                
                print(f"    Full Element Structure:")
                print(f"    {json.dumps(element, indent=6, ensure_ascii=False)}")
                print("-" * 60)
        
        # Save full response to file for detailed analysis
        with open('debug_slides_response.json', 'w', encoding='utf-8') as f:
            json.dump(presentation, f, indent=2, ensure_ascii=False)
        
        print("✅ Full API response saved to 'debug_slides_response.json'")
        
    except Exception as e:
        print(f"❌ Error debugging API response: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(debug_slides_api_response())
